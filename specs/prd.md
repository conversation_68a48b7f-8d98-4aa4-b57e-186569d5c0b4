# Product Requirements Document (PRD): Cypher Labs AI Prompt Manager v2

## Document Information
- Version: 1.0
- Date: 2024/MM/DD

***

## Project Objectives

**Goal**: Create a streamlined and efficient workflow for users to manage, customize, and utilize AI-generated prompts for content creation

**Key Themes**:
- Streamlined user interaction with AI-generated content
- Robust organizational tools for prompt management
- Secure local data storage for offline access

## Core Features

1. **AI-Enhanced Prompt Generation**:
   - Integrate seamless AI-generated text suggestions based on user input prompts
   - Multiple generation model integration (optional Ollama support)

2. **Version Control & Tracking**:
   - Advanced version tracking for prompt history
   - Automatic version incrementation on edits
   - Comprehensive revision history with diff views

3. **Prompt Metadata Management**:
   - Comprehensive categorization through hierarchical tags
   - Enhanced metadata storage: last used timestamp, creation date, usage counters
   - Batch editing functionality for metadata

4. **Data Import/Export & Sharing**:
   - Standardized cross-platform JSON import/export format
   - One-click data backup and restore functionality
   - Secure, encrypted local storage (IndexedDB)

5. **User Interface Enhancements**:
   - Dark/Light theme switcher for accessibility
   - Accessible modal systems with ARIA labels
   - Responsive design for desktop/tablet/mobile compatibility

6. **Utility Function Expansion**:
   - Improved template tag extraction (supports multiple syntax variations)
   - Robust date formatting with localization support

## User Role Stories

1. **Content Creators**:
   - Generate new content ideas quickly using template-based prompts
   - Maintain a well-organized library of prompts based on different use cases

2. **Knowledge Workers**: 
   - Efficiently refine, track and reuse prompts for professional communication
   - Seamlessly transition between project-specific prompt sets

3. **Researchers & Analysts**:
   - Develop comprehensive prompt templates for data analysis tasks
   - Maintain secure archives of research prompts with contextual metadata

## Technical Specifications

### Frontend Components
- Header: Navigation bar with export, import, theme toggler
- Modal: Base component for consistent UX
- AIGenerationModal: Interface for AI generation and copy functions
- PaginationControls: Component reused for prompt listings and version history navigation
- PromptForm: Enhanced with tag input fields for metadata
- TagInput: Tag management UI component
- PromptTile: Updated tile view with version history indicator and AI action button
- FillTemplateModal: Maintained but repurposed for templated workflows

### Hooks & Backend Logic
- **useTheme**: Manages light/dark theme persistence
- **useLocalStorage**: Provides synchronized local storage hooks  
- **paginateItems**: Helper function for server-side prompt pagination

### Data Models and Storage
- **Prompt Schema**: Core object with id, version, createdAt, tags
- **IndexedDB Schema**: AIPMPRDB with prompts store and indexed versions
- **PromptHistory**: Separate table for version tracking and diff analysis

# Development Roadmap

**Phase 1: Foundation**
- Complete AI model integration (core API connection)
- Refactor PromptTile with version tracking support
- Implement PaginationControls for long prompt lists
- Create basic prompt export/import functionality

**Phase 2: Features**
- Fully implement tag hierarchy management
- Build out full version history interface with diff capabilities
- Complete TemplateUtils for multi-bracket support
- Develop responsive/theming updates across all UI components

**Phase 3: Refinement**
- Improve AI-generated text refinement suggestions
- Create automated prompt optimization workflow
- Implement UI/UX enhancements from user testing
- Integrate additional data visualization features for prompt analytics

**Phase 4: Launch Readiness**
- Comprehensive browser testing on core features
- Final security audit of local storage mechanisms
- Packaging for Electron deployment
- Cross-platform distribution setup

---

**Note to Development Team:** The PRD will be updated as development progresses. Version 1.0 focuses primarily on core functionality and user interface requirements based on current architecture elements analyzed during the planning phase.

---

**Branch Naming Conventions (Appendix A)**
1. Main Branch:
   - The main branch is the primary branch of the repository.
   - It must always be in a stable, deployable state.
   - Direct commits to main are prohibited.

2. Feature Branches:
   - All new work (features, bug fixes, chores) must be done on a separate branch, created from main.
   - Branch Naming Convention: To keep branches organized, we use a prefix-based naming convention:
     - feature/<short-hyphenated-description> (e.g., feature/user-authentication)
     - fix/<short-hyphenated-description> (e.g., fix/login-button-bug)
     - chore/<short-hyphenated-description> (e.g., chore/update-dependencies)

3. Commits:
   - Atomic Commits: Each commit should represent a single, logical change.
   - Commit Message Convention: We adhere to the Conventional Commits specification (see Conventional Commits for reference). This improves readability and allows for automated changelog generation. 

4. Merging:
   - When a feature branch is complete and all tests and preflight checks (npm run preflight) pass, it should be merged into main via a Pull Request (PR).
   - PRs allow for code review and ensure quality gates meet before changes are integrated.

**Branch Management Best Practices (Appendix B)**
1. Always pull the latest `main` before starting new work.
2. For long-running features, regularly pull `main` into your branch:
   ```
   git checkout feature/my-work
   git merge origin/main 
   ```
   Note: Avoid 'git pull' in feature branches to minimize unnecessary history merge commits.

3. When resolving merge conflicts:
   - Address changes in discrete commits
   - Prefer squash-merging after conflict resolution

4. Maintain clean commit history:
   - Use `--squash` commits for PRs that combine logical work units
   - Avoid committing dependencies or temporary files

5. Resolve dependency versions with care:
   - Use `--save-exact` flags where possible
   - When upgrading packages, do so in isolation (separate PRs for dependency changes)

**Commit Message Guidelines (Appendix C)**
- Required Format: `<type>[optional scope]: <description>`
  - Types: feat, fix, docs, style, refactor, perf, test, build, ci, chore, revert
  - Scope (optional): `api`, `build`, `db`, `footer`, `ui`, etc. where relevant

Examples:
