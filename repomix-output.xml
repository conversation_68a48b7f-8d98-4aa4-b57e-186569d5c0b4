This file is a merged representation of the entire codebase, combined into a single document by <PERSON>omix.

<file_summary>
This section contains a summary of this file.

<purpose>
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.
</purpose>

<file_format>
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files (if enabled)
5. Multiple file entries, each consisting of:
  - File path as an attribute
  - Full contents of the file
</file_format>

<usage_guidelines>
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.
</usage_guidelines>

<notes>
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Files are sorted by Git change count (files with more changes are at the bottom)
</notes>

</file_summary>

<directory_structure>
components/
  icons/
    ChevronDownIcon.tsx
    ChevronLeftIcon.tsx
    ChevronRightIcon.tsx
    ClipboardIcon.tsx
    DownloadIcon.tsx
    FolderIcon.tsx
    HistoryIcon.tsx
    MoonIcon.tsx
    PencilIcon.tsx
    PlusIcon.tsx
    QuestionMarkCircleIcon.tsx
    SparklesIcon.tsx
    SunIcon.tsx
    TagIcon.tsx
    TrashIcon.tsx
    UploadIcon.tsx
  AIGenerationModal.tsx
  FillTemplateModal.tsx
  Header.tsx
  HelpModal.tsx
  IconButton.tsx
  Modal.tsx
  PaginationControls.tsx
  PromptForm.tsx
  PromptTile.tsx
  Sidebar.tsx
  TagInput.tsx
  ThemeToggleButton.tsx
  VersionHistoryModal.tsx
hooks/
  useLocalStorage.ts
  useTheme.ts
utils/
  dateFormatter.ts
  idb.ts
  ollamaApi.ts
  tagUtils.ts
  templateUtils.ts
  uuid.ts
.gitignore
App.tsx
constants.ts
dev_runner.txt
dummy-prompts.json
index.html
index.tsx
metadata.json
package.json
README.md
repomix-output.txt
tsconfig.json
types.ts
vite.config.ts
</directory_structure>

<files>
This section contains the contents of the repository's files.

<file path="utils/tagUtils.ts">
import { Prompt } from './types';

export interface HierarchicalTag {
  name: string;
  fullName: string;
  children: HierarchicalTag[];
}

export const buildTagHierarchy = (prompts: Prompt[]): HierarchicalTag[] => {
  const allTags = new Set<string>();
  prompts.forEach(p => p.tags.forEach(t => allTags.add(t)));

  const root: HierarchicalTag = { name: 'root', fullName: 'root', children: [] };

  allTags.forEach(tag => {
    const parts = tag.split('/');
    let currentNode = root;
    let currentFullName = '';

    parts.forEach((part, index) => {
      currentFullName = currentFullName ? `${currentFullName}/${part}` : part;
      let childNode = currentNode.children.find(c => c.name === part);
      if (!childNode) {
        childNode = { name: part, fullName: currentFullName, children: [] };
        currentNode.children.push(childNode);
      }
      currentNode = childNode;
    });
  });

  return root.children.sort((a, b) => a.name.localeCompare(b.name));
};

export const getAllTagIdsInBranch = (tag: HierarchicalTag): string[] => {
  const ids: string[] = [tag.fullName];
  tag.children.forEach(child => {
    ids.push(...getAllTagIdsInBranch(child));
  });
  return ids;
};
</file>

<file path="repomix-output.txt">
This file is a merged representation of a subset of the codebase, containing specifically included files, combined into a single document by Repomix.
The content has been processed where comments have been removed, empty lines have been removed, content has been compressed (code blocks are separated by ⋮---- delimiter).

================================================================
File Summary
================================================================

Purpose:
--------
This file contains a packed representation of a subset of the repository's contents that is considered the most important context.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.

File Format:
------------
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files (if enabled)
5. Multiple file entries, each consisting of:
  a. A separator line (================)
  b. The file path (File: path/to/file)
  c. Another separator line
  d. The full contents of the file
  e. A blank line

Usage Guidelines:
-----------------
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.

Notes:
------
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Only files matching these patterns are included: src/**/*.cs
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Code comments have been removed from supported file types
- Empty lines have been removed from all files
- Content has been compressed - code blocks are separated by ⋮---- delimiter
- Files are sorted by Git change count (files with more changes are at the bottom)


================================================================
Directory Structure
================================================================


================================================================
Files
================================================================




================================================================
End of Codebase
================================================================
</file>

<file path="components/icons/ChevronDownIcon.tsx">
import React from 'react';

const ChevronDownIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="m19.5 8.25-7.5 7.5-7.5-7.5" />
  </svg>
);

export default ChevronDownIcon;
</file>

<file path="components/icons/ChevronLeftIcon.tsx">
import React from 'react';

const ChevronLeftIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 19.5 8.25 12l7.5-7.5" />
  </svg>
);

export default ChevronLeftIcon;
</file>

<file path="components/icons/ChevronRightIcon.tsx">
import React from 'react';

const ChevronRightIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="m8.25 4.5 7.5 7.5-7.5 7.5" />
  </svg>
);

export default ChevronRightIcon;
</file>

<file path="components/icons/ClipboardIcon.tsx">
import React from 'react';

const ClipboardIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M15.666 3.888A2.25 2.25 0 0 0 13.5 2.25h-3c-1.03 0-1.9.693-2.166 1.638m7.332 0c.*************.084.612v0a.75.75 0 0 1-.75.75H9a.75.75 0 0 1-.75-.75v0c0-.212.03-.418.084-.612m7.332 0c.646.049 1.288.11 1.927.184 1.1.128 1.907 1.077 1.907 2.185V19.5a2.25 2.25 0 0 1-2.25 2.25H6.75A2.25 2.25 0 0 1 4.5 19.5V6.257c0-1.108.806-2.057 1.907-2.185a48.208 48.208 0 0 1 1.927-.184" />
  </svg>
);
export default ClipboardIcon;
</file>

<file path="components/icons/DownloadIcon.tsx">
import React from 'react';

const DownloadIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5M16.5 12 12 16.5m0 0L7.5 12m4.5 4.5V3" />
  </svg>
);

export default DownloadIcon;
</file>

<file path="components/icons/FolderIcon.tsx">
import React from 'react';

const FolderIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M2.25 12.75V12A2.25 2.25 0 0 1 4.5 9.75h15A2.25 2.25 0 0 1 21.75 12v.75m-8.69-6.44-2.12-2.12a1.5 1.5 0 0 0-1.061-.44H4.5A2.25 2.25 0 0 0 2.25 6v12a2.25 2.25 0 0 0 2.25 2.25h15A2.25 2.25 0 0 0 21.75 18V9a2.25 2.25 0 0 0-2.25-2.25h-5.379a1.5 1.5 0 0 1-1.06-.44Z" />
  </svg>
);
export default FolderIcon;
</file>

<file path="components/icons/HistoryIcon.tsx">
import React from 'react';

const HistoryIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
  </svg>
);

export default HistoryIcon;
</file>

<file path="components/icons/MoonIcon.tsx">
import React from 'react';

const MoonIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M21.752 15.002A9.72 9.72 0 0 1 18 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 0 0 3 11.25C3 16.635 7.365 21 12.75 21c3.978 0 7.443-2.03 9.002-5.001Z" />
  </svg>
);

export default MoonIcon;
</file>

<file path="components/icons/PencilIcon.tsx">
import React from 'react';

const PencilIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10" />
  </svg>
);
export default PencilIcon;
</file>

<file path="components/icons/PlusIcon.tsx">
import React from 'react';

const PlusIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
  </svg>
);
export default PlusIcon;
</file>

<file path="components/icons/QuestionMarkCircleIcon.tsx">
import React from 'react';

const QuestionMarkCircleIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M9.879 7.519c1.171-1.025 3.071-1.025 4.242 0 1.172 1.025 1.172 2.687 0 3.712-.203.179-.43.326-.67.442-.745.361-1.45.999-1.45 1.827v.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 5.25h.008v.008H12v-.008Z" />
  </svg>
);

export default QuestionMarkCircleIcon;
</file>

<file path="components/icons/SparklesIcon.tsx">
import React from 'react';

const SparklesIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09ZM18.25 7.5l.813 2.846a4.5 4.5 0 012.103 2.102L24 13.5l-2.846.813a4.5 4.5 0 01-2.102 2.102L16.5 19.5l-.813-2.846a4.5 4.5 0 01-2.102-2.102L10.5 13.5l2.846-.813a4.5 4.5 0 012.102-2.102L18.25 7.5Z" />
  </svg>
);

export default SparklesIcon;
</file>

<file path="components/icons/SunIcon.tsx">
import React from 'react';

const SunIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M12 3v2.25m6.364.386-1.591 1.591M21 12h-2.25m-.386 6.364-1.591-1.591M12 18.75V21m-6.364-.386 1.591-1.591M3 12h2.25m.386-6.364 1.591 1.591M12 12a2.25 2.25 0 0 0-2.25 2.25 2.25 2.25 0 0 0 2.25 2.25 2.25 2.25 0 0 0 2.25-2.25A2.25 2.25 0 0 0 12 12Z" />
  </svg>
);

export default SunIcon;
</file>

<file path="components/icons/TagIcon.tsx">
import React from 'react';

const TagIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M9.568 3H5.25A2.25 2.25 0 0 0 3 5.25v4.318c0 .597.237 1.17.659 1.591l9.581 9.581c.699.699 1.78.872 2.607.33a18.095 18.095 0 0 0 5.223-5.223c.542-.827.369-1.908-.33-2.607L11.16 3.66A2.25 2.25 0 0 0 9.568 3Z" />
    <path strokeLinecap="round" strokeLinejoin="round" d="M6 6h.008v.008H6V6Z" />
  </svg>
);
export default TagIcon;
</file>

<file path="components/icons/TrashIcon.tsx">
import React from 'react';

const TrashIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12.56 0c1.153 0 2.24.032 3.22.076M6 6l.245.924L6.25 9h11.5l.25-2.076L18 6M6 6h12M6 6c0-.552.448-1 1-1h10c.552 0 1 .448 1 1M6 6v0" />
  </svg>
);
export default TrashIcon;
</file>

<file path="components/icons/UploadIcon.tsx">
import React from 'react';

const UploadIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" {...props}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5" />
  </svg>
);

export default UploadIcon;
</file>

<file path="components/AIGenerationModal.tsx">
import React, { useState, useEffect } from 'react';
import Modal from './Modal';
import ClipboardIcon from './icons/ClipboardIcon';
import IconButton from './IconButton';

interface AIGenerationModalProps {
  isOpen: boolean;
  onClose: () => void;
  promptTitle: string;
  inputText: string;
  generatedText: string | null;
  isGenerating: boolean;
  onGenerate: (textToGenerateFrom: string) => void;
  errorMessage?: string | null; 
}

const AIGenerationModal: React.FC<AIGenerationModalProps> = ({
  isOpen,
  onClose,
  promptTitle,
  inputText,
  generatedText,
  isGenerating,
  onGenerate,
  errorMessage,
}) => {
  const [copiedOutput, setCopiedOutput] = useState(false);

  useEffect(() => {
    if (isOpen) {
      setCopiedOutput(false); // Reset copied state when modal opens/re-opens
    }
  }, [isOpen]);

  const handleCopyToClipboard = () => {
    if (generatedText) {
      navigator.clipboard.writeText(generatedText).then(() => {
        setCopiedOutput(true);
        setTimeout(() => setCopiedOutput(false), 2000);
      }).catch(err => {
        console.error('Failed to copy AI generated text: ', err);
        alert('Failed to copy text.');
      });
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} title={`Generate with AI: "${promptTitle}"`}>
      <div className="space-y-6">
        <div>
          <label htmlFor="ai-input-prompt" className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1">
            Your Prompt:
          </label>
          <textarea
            id="ai-input-prompt"
            readOnly
            value={inputText}
            rows={5}
            className="w-full p-2 border border-neutral-300 dark:border-neutral-600 rounded-md shadow-sm bg-neutral-50 dark:bg-neutral-700 text-neutral-900 dark:text-neutral-100 resize-none"
            aria-label="Input prompt text for AI generation"
          />
        </div>

        <div className="flex justify-center">
          <button
            type="button"
            onClick={() => onGenerate(inputText)}
            disabled={isGenerating}
            className="px-6 py-2 text-sm font-medium text-white bg-primary hover:bg-primary-dark rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-neutral-800 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isGenerating ? 'Generating...' : (generatedText ? 'Regenerate' : 'Generate Text')}
          </button>
        </div>

        {errorMessage && !isGenerating && (
            <div className="p-3 bg-red-100 dark:bg-red-900 border border-red-300 dark:border-red-700 rounded-md text-red-700 dark:text-red-200 text-sm">
                <p><strong>Error:</strong> {errorMessage}</p>
            </div>
        )}

        {(isGenerating || generatedText) && !errorMessage && (
          <div>
            <div className="flex justify-between items-center mb-1">
              <label htmlFor="ai-generated-output" className="block text-sm font-medium text-neutral-700 dark:text-neutral-300">
                AI Output:
              </label>
              {generatedText && !isGenerating && (
                <IconButton
                  onClick={handleCopyToClipboard}
                  label={copiedOutput ? "Copied!" : "Copy AI output"}
                  className="text-xs p-1"
                >
                  <ClipboardIcon className="w-4 h-4 mr-1" />
                  {copiedOutput ? "Copied!" : "Copy Output"}
                </IconButton>
              )}
            </div>
            {isGenerating ? (
              <div className="flex items-center justify-center h-32 p-2 border border-neutral-300 dark:border-neutral-600 rounded-md bg-neutral-50 dark:bg-neutral-700">
                <svg className="animate-spin h-8 w-8 text-primary dark:text-primary-light" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span className="ml-3 text-neutral-600 dark:text-neutral-300">Generating text...</span>
              </div>
            ) : (
              generatedText && (
                <textarea
                  id="ai-generated-output"
                  readOnly
                  value={generatedText}
                  rows={8}
                  className="w-full p-2 border border-neutral-300 dark:border-neutral-600 rounded-md shadow-sm bg-neutral-50 dark:bg-neutral-700 text-neutral-900 dark:text-neutral-100 resize-y"
                  aria-label="AI generated text output"
                />
              )
            )}
          </div>
        )}

        <div className="flex justify-end mt-6">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-neutral-700 dark:text-neutral-200 bg-neutral-100 dark:bg-neutral-600 border border-neutral-300 dark:border-neutral-500 rounded-md shadow-sm hover:bg-neutral-200 dark:hover:bg-neutral-500 focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-neutral-800 focus:ring-primary-light"
          >
            Close
          </button>
        </div>
      </div>
    </Modal>
  );
};

export default AIGenerationModal;
</file>

<file path="components/FillTemplateModal.tsx">
import React, { useState, useEffect, useCallback } from 'react';
import { Prompt } from '../types';
import { extractVariables, fillTemplate } from '../utils/templateUtils';
import Modal from './Modal';

interface FillTemplateModalProps {
  isOpen: boolean;
  onClose: () => void;
  templatePrompt: Prompt;
  onGenerateAndCopy: (originalPrompt: Prompt, filledText: string) => void; // Modified to pass original prompt
}

const FillTemplateModal: React.FC<FillTemplateModalProps> = ({
  isOpen,
  onClose,
  templatePrompt,
  onGenerateAndCopy,
}) => {
  const [variables, setVariables] = useState<string[]>([]);
  const [variableValues, setVariableValues] = useState<Record<string, string>>({});
  const [copied, setCopied] = useState(false);

  useEffect(() => {
    if (isOpen && templatePrompt) {
      const extracted = extractVariables(templatePrompt.promptText);
      setVariables(extracted);
      const initialValues: Record<string, string> = {};
      extracted.forEach(v => initialValues[v] = '');
      setVariableValues(initialValues);
      setCopied(false); 
    }
  }, [isOpen, templatePrompt]);

  const handleValueChange = (variableName: string, value: string) => {
    setVariableValues(prev => ({ ...prev, [variableName]: value }));
  };

  const handleGenerateAndCopyClick = () => {
    const filledText = fillTemplate(templatePrompt.promptText, variableValues);
    // For normal copy, show "Copied!" and close. For AI flow, App.tsx will handle modal transitions.
    // The onGenerateAndCopy callback now signals completion to App.tsx.
    onGenerateAndCopy(templatePrompt, filledText); 
    
    // Show "Copied!" feedback if not immediately transitioning to another modal
    // This visual feedback might be less relevant if App.tsx immediately opens AI modal
    // For now, let's assume onGenerateAndCopy itself might trigger UI changes in App.tsx
    // which might include closing this modal or opening another.
    // We can set copied for a brief moment for visual feedback regardless.
    navigator.clipboard.writeText(filledText).then(() => {
        setCopied(true);
        setTimeout(() => {
            setCopied(false);
            // onClose(); // App.tsx will now control closing based on flow.
        }, 1500);
    }).catch(err => {
        console.error('Failed to copy filled template: ', err);
        alert('Failed to copy text.');
    });
  };

  if (!isOpen || !templatePrompt) return null;

  return (
    <Modal isOpen={isOpen} onClose={onClose} title={`Use Template: "${templatePrompt.title}"`}>
      <div className="space-y-4">
        <p className="text-sm text-neutral-600 dark:text-neutral-300">
          Fill in the values for the variables in this prompt template.
        </p>
        
        {variables.length > 0 ? (
          variables.map(variable => (
            <div key={variable}>
              <label htmlFor={`var-${variable}`} className="block text-sm font-medium text-neutral-700 dark:text-neutral-200 capitalize">
                {variable.replace(/_/g, ' ')}
              </label>
              <input
                type="text"
                id={`var-${variable}`}
                value={variableValues[variable] || ''}
                onChange={(e) => handleValueChange(variable, e.target.value)}
                className="mt-1 block w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm bg-white dark:bg-neutral-700 text-neutral-900 dark:text-neutral-100"
              />
            </div>
          ))
        ) : (
          <p className="text-neutral-500 dark:text-neutral-400">No variables found in this prompt.</p>
        )}

        <div className="flex justify-end space-x-3 pt-4">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-neutral-700 dark:text-neutral-200 bg-neutral-100 dark:bg-neutral-600 border border-neutral-300 dark:border-neutral-500 rounded-md shadow-sm hover:bg-neutral-200 dark:hover:bg-neutral-500 focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-neutral-800 focus:ring-primary-light"
          >
            Cancel
          </button>
          <button
            type="button"
            onClick={handleGenerateAndCopyClick}
            disabled={variables.length === 0 || copied}
            className={`px-4 py-2 text-sm font-medium text-white rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-neutral-800 focus:ring-primary
                        ${copied ? 'bg-green-500 hover:bg-green-600' : 'bg-primary hover:bg-primary-dark'}
                        ${variables.length === 0 ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            {copied ? 'Copied & Done!' : 'Complete & Use Text'}
          </button>
        </div>
      </div>
    </Modal>
  );
};

export default FillTemplateModal;
</file>

<file path="components/Header.tsx">
import React from 'react';
import PlusIcon from './icons/PlusIcon';
import ThemeToggleButton from './ThemeToggleButton';
import QuestionMarkCircleIcon from './icons/QuestionMarkCircleIcon';
import DownloadIcon from './icons/DownloadIcon'; 
import UploadIcon from './icons/UploadIcon';   
import IconButton from './IconButton';

interface HeaderProps {
  onAddPrompt: () => void;
  onToggleHelp: () => void;
  onExportData: () => void; 
  onImportDataTrigger: () => void; 
}

const Header: React.FC<HeaderProps> = ({ onAddPrompt, onToggleHelp, onExportData, onImportDataTrigger }) => {
  return (
    <header className="bg-white dark:bg-neutral-800 shadow-md dark:shadow-neutral-700/50 sticky top-0 z-40 transition-colors duration-300">
      {/* Removed 'container mx-auto' and adjusted padding for full width */}
      <div className="w-full px-4 sm:px-6 py-4 flex justify-between items-center">
        {/* App Title - Responsive */}
        <div className="flex-shrink-0">
          <h1 className="font-bold text-primary-dark dark:text-primary-light flex items-center">
            <span role="img" aria-label="brain emoji" className="mr-2 text-2xl">🧠</span>
            <span className="hidden sm:inline text-xl md:text-2xl">AI Prompt Manager</span>
            <span className="sm:hidden text-xl">Prompts</span>
          </h1>
        </div>
        
        {/* Action Buttons - Responsive */}
        <div className="flex items-center space-x-1 sm:space-x-2 flex-shrink-0">
          <ThemeToggleButton />
          <IconButton 
            onClick={onImportDataTrigger} 
            label="Import Data"
            className="text-neutral-600 dark:text-neutral-300 hover:text-primary dark:hover:text-primary-light"
            title="Import Data (replaces existing data)"
          >
            <UploadIcon className="w-5 h-5 sm:w-6 sm:h-6" />
          </IconButton>
           <IconButton 
            onClick={onExportData} 
            label="Export Data"
            className="text-neutral-600 dark:text-neutral-300 hover:text-primary dark:hover:text-primary-light"
            title="Export All Data"
          >
            <DownloadIcon className="w-5 h-5 sm:w-6 sm:h-6" />
          </IconButton>
          <IconButton 
            onClick={onToggleHelp} 
            label="Help"
            className="text-neutral-600 dark:text-neutral-300 hover:text-primary dark:hover:text-primary-light"
          >
            <QuestionMarkCircleIcon className="w-6 h-6" />
          </IconButton>
          
          {/* Responsive Add Prompt Button */}
          <button
            onClick={onAddPrompt}
            className="hidden sm:flex items-center bg-primary hover:bg-primary-dark text-white font-semibold py-2 px-3 md:px-4 rounded-lg shadow-md hover:shadow-lg transition-all duration-150 ease-in-out text-sm md:text-base"
            title="Add New Prompt"
          >
            <PlusIcon className="w-5 h-5 mr-1 md:mr-2" />
            <span className="hidden md:inline">Add Prompt</span>
            <span className="md:hidden">Add</span>
          </button>
          {/* Icon-only Add Prompt Button for xs screens */}
          <IconButton
            onClick={onAddPrompt}
            label="Add new prompt"
            className="sm:hidden text-white bg-primary hover:bg-primary-dark p-2" 
            title="Add New Prompt"
          >
            <PlusIcon className="w-5 h-5" />
          </IconButton>
        </div>
      </div>
    </header>
  );
};

export default Header;
</file>

<file path="components/HelpModal.tsx">
import React, { useState } from 'react';
import Modal from './Modal';
import { Prompt, Folder } from '../types'; // For referencing types in advanced section

type HelpTab = 'guide' | 'advanced';

interface HelpModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const HelpModalContent: React.FC = () => {
  const [activeTab, setActiveTab] = useState<HelpTab>('guide');

  const renderUserGuide = () => (
    <div className="space-y-6 text-neutral-700 dark:text-neutral-300">
      <section>
        <h3 className="text-xl font-semibold text-primary-dark dark:text-primary-light mb-2">Welcome to your AI Prompt Manager!</h3>
        <p>This guide helps you get the most out of the app.</p>
      </section>

      <section>
        <h4 className="text-lg font-semibold mb-1">Core Features</h4>
        <ul className="list-disc list-inside space-y-1 pl-2">
          <li><strong>Add Prompts:</strong> Click "Add New Prompt" in the header. Fill in the title, description (optional), the prompt text itself, tags (optional), and assign it to a folder.</li>
          <li><strong>Edit Prompts:</strong> Click the pencil icon on a prompt tile. Editing a prompt saves it as a new version (see "Prompt Versioning" below).</li>
          <li><strong>Delete Prompts:</strong> Click the trash icon on a prompt tile. This deletes the prompt and all its previous versions.</li>
          <li><strong>Manage Folders:</strong> Use the sidebar to create, rename, and delete folders. Folders can be nested. A folder must be empty (no prompts and no subfolders) before it can be deleted.</li>
          <li><strong>Copy to Clipboard:</strong> Click the "Copy Prompt" button on a tile to quickly copy the prompt text. This also updates its "Last Used" date and "Times Used" counter.</li>
        </ul>
      </section>
      
      <section>
        <h4 className="text-lg font-semibold mb-1">Prompt Versioning</h4>
        <p>Never lose a good iteration! When you edit an existing prompt and save it, the app automatically creates a new version.</p>
        <ul className="list-disc list-inside space-y-1 pl-2">
          <li>The main grid always displays the latest version of each prompt.</li>
          <li>If a prompt has multiple versions, a "history" icon will appear on its tile. Click this to open the "Version History" modal.</li>
          <li>In the history modal, you can see all previous versions, their creation dates, and their content.</li>
        </ul>
      </section>

      <section>
        <h4 className="text-lg font-semibold mb-1">Prompt Templating & Variables</h4>
        <p>Make your prompts more reusable with variables. This is useful for prompts that have a similar structure but need slight changes for different contexts.</p>
        <ul className="list-disc list-inside space-y-1 pl-2">
          <li><strong>Defining Variables:</strong> In the "Prompt Text" field, use double curly braces to define a variable, like <code>&#123;&#123;topic&#125;&#125;</code> or <code>&#123;&#123;user_name&#125;&#125;</code>.</li>
          <li><strong>Using Templates:</strong> If a prompt contains variables, its "Copy Prompt" button will change to "Use Template".
            <ul className="list-disc list-inside space-y-1 pl-4 mt-1">
              <li>Clicking "Use Template" opens a modal where you'll see input fields for each variable you defined.</li>
              <li>Fill in the values for your variables.</li>
              <li>Click "Complete & Use Text". This will create the final prompt string with your values filled in and copy it to your clipboard.</li>
              <li>Using a template also updates the original template prompt's "Last Used" date and "Times Used" counter.</li>
            </ul>
          </li>
          <li><strong>Example:</strong> If your prompt text is <code>Write a short story about a brave knight named &#123;&#123;knight_name&#125;&#125; who must retrieve the &#123;&#123;magical_item&#125;&#125;.</code>, the "Use Template" modal will ask for "knight_name" and "magical_item".</li>
        </ul>
      </section>

       <section>
        <h4 className="text-lg font-semibold mb-1">🤖 AI Text Generation (with Ollama)</h4>
        <p>Leverage your local Ollama instance to generate text directly from your prompts.</p>
        <ul className="list-disc list-inside space-y-1 pl-2">
          <li><strong>Using the Feature:</strong>
            <ul className="list-disc list-inside space-y-1 pl-4 mt-1">
              <li>Click the "Sparkles" icon (✨) on any prompt tile.</li>
              <li>If the prompt is a template (contains variables like <code>&#123;&#123;variable&#125;&#125;</code>), you'll first be asked to fill in those variables.</li>
              <li>The "AI Generation Modal" will then open, showing the (filled) prompt text.</li>
              <li>Click "Generate Text" to send the prompt to your Ollama instance.</li>
              <li>The AI's response will appear in the "AI Output" area. You can copy this output using the "Copy Output" button.</li>
            </ul>
          </li>
          <li><strong>Important Setup:</strong>
            <ul className="list-disc list-inside space-y-1 pl-4 mt-1">
              <li>This feature requires a running local Ollama instance.</li>
              <li>You <strong className="text-amber-600 dark:text-amber-400">must</strong> configure two environment variables for your application:
                <ul className="list-disc list-inside space-y-1 pl-4 mt-1">
                  <li><code>OLLAMA_API_ENDPOINT</code>: The full URL to your Ollama API's generate endpoint (e.g., <code>http://localhost:11434/api/generate</code>). If using Vite, you might name it <code>VITE_OLLAMA_API_ENDPOINT</code> in your <code>.env</code> file.</li>
                  <li><code>OLLAMA_MODEL_NAME</code>: The name of the Ollama model you want to use (e.g., <code>llama3</code>, <code>mistral</code>). If using Vite, you might name it <code>VITE_OLLAMA_MODEL_NAME</code>.</li>
                </ul>
              </li>
               <li>Ensure these variables are accessible by your frontend JavaScript code (e.g., via <code>process.env.OLLAMA_API_ENDPOINT</code> or <code>import.meta.env.VITE_OLLAMA_API_ENDPOINT</code> if using Vite).</li>
            </ul>
          </li>
          <li><strong>Error Handling:</strong> If the environment variables are not set, Ollama is unreachable, or the model name is invalid, you will receive an error message.</li>
        </ul>
      </section>

      <section>
        <h4 className="text-lg font-semibold mb-1">Sorting & Filtering</h4>
        <ul className="list-disc list-inside space-y-1 pl-2">
          <li><strong>Sort:</strong> Use the dropdown above the prompt grid to sort by name, most used, recently used, or date created.</li>
          <li><strong>Search:</strong> Use the search bar to find prompts by title, description, or prompt text.</li>
          <li><strong>Filter by Tag:</strong> If you have tags, a tag filter dropdown will appear, allowing you to see only prompts with a specific tag.</li>
          <li><strong>Filter by Folder:</strong> Click on a folder in the sidebar to view only prompts within that folder and its subfolders. "All Prompts" shows everything, and "Uncategorized" shows prompts not in a user-created folder.</li>
        </ul>
      </section>

       <section>
        <h4 className="text-lg font-semibold mb-1">Light/Dark Mode</h4>
        <p>Click the sun/moon icon in the header to toggle between light and dark themes for comfortable viewing.</p>
      </section>

      <section>
        <h4 className="text-lg font-semibold mb-1">Import & Export Data</h4>
        <ul className="list-disc list-inside space-y-1 pl-2">
            <li><strong>Export Data:</strong> Click the "Download" icon in the header to save all your prompts and folders as a JSON file (<code>ai-prompt-manager-backup-YYYY-MM-DD.json</code>) to your computer. This is useful for backups or transferring your data.</li>
            <li><strong>Import Data:</strong> Click the "Upload" icon in the header to select a previously exported JSON backup file.
                <ul className="list-disc list-inside space-y-1 pl-4 mt-1">
                    <li><strong className="text-red-600 dark:text-red-400">CRITICAL WARNING:</strong> Importing data will <strong className="text-red-600 dark:text-red-400">REPLACE ALL</strong> current prompts and folders in the application. This action <strong className="text-red-600 dark:text-red-400">CANNOT BE UNDONE</strong>. You will be asked to confirm before the import proceeds.</li>
                    <li>Ensure the JSON file is a valid backup created by this application.</li>
                </ul>
            </li>
        </ul>
      </section>
    </div>
  );

  const renderAdvancedInfo = () => (
    <div className="space-y-6 text-sm text-neutral-700 dark:text-neutral-300">
      <section>
        <h3 className="text-xl font-semibold text-primary-dark dark:text-primary-light mb-2">Advanced Information & Data Structure</h3>
        <p>This section describes how your data is stored locally in your browser and how the import/export feature works.</p>
      </section>

      <section>
        <h4 className="text-lg font-semibold mb-1">Storage Mechanism</h4>
        <p>Your prompts and folders are stored in your web browser's <strong>IndexedDB</strong>. This is a client-side database, meaning the data resides on your computer within the browser you are using. It is not automatically synced to any cloud service.</p>
        <ul className="list-disc list-inside space-y-1 pl-2">
          <li><strong>Database Name:</strong> <code>AIPromptManagerDB</code></li>
          <li><strong>Object Stores (Tables):</strong>
            <ul className="list-disc list-inside space-y-1 pl-4 mt-1">
              <li><code>prompts</code>: Stores all versions of all your prompts.</li>
              <li><code>folders</code>: Stores your folder structure.</li>
            </ul>
          </li>
        </ul>
        <p className="mt-2">You can inspect IndexedDB data using your browser's developer tools (usually under the "Application" or "Storage" tab).</p>
      </section>

      <section>
        <h4 className="text-lg font-semibold mb-1">Data Structures (as used in Export/Import)</h4>
        <p>The JSON file used for import/export contains an object with two main arrays: <code>prompts</code> and <code>folders</code>.</p>
        
        <div className="mt-3">
          <h5 className="font-semibold mb-1">Prompt Object:</h5>
          <pre className="bg-neutral-100 dark:bg-neutral-700 p-3 rounded-md text-xs overflow-x-auto">
            {`{
  "id": "string", // Unique ID for this specific version
  "originalPromptId": "string", // ID of the first version
  "version": "number",
  "title": "string",
  "description": "string",
  "promptText": "string",
  "tags": ["string"],
  "folderId": "string | null",
  "createdAt": "string_iso_date",
  "lastUsedAt": "string_iso_date | null",
  "timesUsed": "number"
}`}
          </pre>
        </div>

        <div className="mt-4">
          <h5 className="font-semibold mb-1">Folder Object:</h5>
          <pre className="bg-neutral-100 dark:bg-neutral-700 p-3 rounded-md text-xs overflow-x-auto">
            {`{
  "id": "string",
  "name": "string",
  "parentId": "string | null",
  "isDeletable": "boolean",
  "isRenamable": "boolean"
}`}
          </pre>
        </div>
      </section>
      
      <section>
        <h4 className="text-lg font-semibold mb-1">Data Portability (Import/Export)</h4>
        <p>The application now includes robust JSON import and export features:</p>
        <ul className="list-disc list-inside space-y-1 pl-2">
            <li><strong>Export:</strong> Creates a comprehensive JSON backup of all your prompts and folders. Save this file in a safe place.</li>
            <li><strong>Import:</strong> Allows you to restore your data from a previously exported JSON file.
                <ul className="list-disc list-inside space-y-1 pl-4 mt-1">
                    <li><strong className="text-red-600 dark:text-red-400">IMPORTANT:</strong> As stated in the user guide, importing will replace all existing data. Always back up your current data via export before importing if you have changes you don't want to lose.</li>
                    <li>The import process validates the basic structure of the file (presence of <code>prompts</code> and <code>folders</code> arrays). However, manually editing the JSON file can lead to import errors if the structure or data types are corrupted.</li>
                </ul>
            </li>
        </ul>
      </section>

      <section>
        <h4 className="text-lg font-semibold mb-1">🤖 AI Text Generation with Ollama (Advanced)</h4>
        <p>The Ollama integration uses the <code>/api/generate</code> endpoint.</p>
        <ul className="list-disc list-inside space-y-1 pl-2">
          <li><strong>Environment Variables:</strong> <code>OLLAMA_API_ENDPOINT</code> (or <code>VITE_OLLAMA_API_ENDPOINT</code>) and <code>OLLAMA_MODEL_NAME</code> (or <code>VITE_OLLAMA_MODEL_NAME</code>) are read from <code>process.env</code> or <code>import.meta.env</code>. Ensure your build process makes these available to your client-side JavaScript.</li>
          <li><strong>Request:</strong> A <code>POST</code> request is made with a JSON body: <code>{`{"model": "your_model_name", "prompt": "your_prompt_text", "stream": false}`}</code>.</li>
          <li><strong>Response:</strong> The application expects a JSON response. The generated text is taken from the <code>response</code> field of this JSON.</li>
          <li><strong>Error Handling:</strong>
            <ul className="list-disc list-inside space-y-1 pl-4 mt-1">
              <li>Checks for missing environment variables.</li>
              <li>Handles network errors if the Ollama endpoint is unreachable.</li>
              <li>Parses errors from Ollama's JSON response if the HTTP request was successful but Ollama signals an error (e.g., model not found).</li>
              <li>Alerts the user with specific error messages for common issues.</li>
            </ul>
          </li>
        </ul>
      </section>
    </div>
  );

  return (
    <div>
      <div className="border-b border-neutral-200 dark:border-neutral-700 mb-4">
        <nav className="flex space-x-4 -mb-px">
          <button
            onClick={() => setActiveTab('guide')}
            className={`py-3 px-4 text-sm font-medium border-b-2
                        ${activeTab === 'guide' 
                          ? 'border-primary text-primary-dark dark:text-primary-light dark:border-primary-light' 
                          : 'border-transparent text-neutral-500 dark:text-neutral-400 hover:text-neutral-700 dark:hover:text-neutral-200 hover:border-neutral-300 dark:hover:border-neutral-600'}`}
          >
            User Guide
          </button>
          <button
            onClick={() => setActiveTab('advanced')}
            className={`py-3 px-4 text-sm font-medium border-b-2
                        ${activeTab === 'advanced' 
                          ? 'border-primary text-primary-dark dark:text-primary-light dark:border-primary-light' 
                          : 'border-transparent text-neutral-500 dark:text-neutral-400 hover:text-neutral-700 dark:hover:text-neutral-200 hover:border-neutral-300 dark:hover:border-neutral-600'}`}
          >
            Advanced Info / Data
          </button>
        </nav>
      </div>
      {activeTab === 'guide' ? renderUserGuide() : renderAdvancedInfo()}
    </div>
  );
};


const HelpModal: React.FC<HelpModalProps> = ({ isOpen, onClose }) => {
  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Help & Information">
      <HelpModalContent />
    </Modal>
  );
};

export default HelpModal;
</file>

<file path="components/IconButton.tsx">
import React from 'react';

interface IconButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  label?: string; // For accessibility
}

const IconButton: React.FC<IconButtonProps> = ({ children, label, className, ...props }) => {
  return (
    <button
      type="button"
      aria-label={label}
      className={`p-2 rounded-md hover:bg-neutral-200 dark:hover:bg-neutral-700 focus:outline-none focus:ring-2 focus:ring-primary-light dark:focus:ring-offset-neutral-800 focus:ring-opacity-50 transition-colors ${className || ''}`}
      {...props}
    >
      {children}
    </button>
  );
};

export default IconButton;
</file>

<file path="components/Modal.tsx">
import React from 'react';

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
}

const Modal: React.FC<ModalProps> = ({ isOpen, onClose, title, children }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 dark:bg-opacity-75 backdrop-blur-sm p-4 transition-opacity duration-300">
      <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-xl w-full max-w-lg max-h-[90vh] flex flex-col transition-colors duration-300">
        <div className="flex items-center justify-between p-4 border-b border-neutral-200 dark:border-neutral-700">
          <h2 className="text-xl font-semibold text-neutral-800 dark:text-neutral-100">{title}</h2>
          <button
            onClick={onClose}
            className="text-neutral-500 dark:text-neutral-400 hover:text-neutral-700 dark:hover:text-neutral-200 transition-colors"
            aria-label="Close modal"
          >
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6">
              <path strokeLinecap="round" strokeLinejoin="round" d="M6 18 18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        <div className="p-6 overflow-y-auto">
          {children}
        </div>
      </div>
    </div>
  );
};

export default Modal;
</file>

<file path="components/PaginationControls.tsx">
import React from 'react';
import IconButton from './IconButton';
import ChevronLeftIcon from './icons/ChevronLeftIcon'; // Assuming you have this or will create
import ChevronRightIcon from './icons/ChevronRightIcon';

interface PaginationControlsProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}

const PaginationControls: React.FC<PaginationControlsProps> = ({ currentPage, totalPages, onPageChange }) => {
  if (totalPages <= 1) {
    return null;
  }

  const handlePrevious = () => {
    if (currentPage > 1) {
      onPageChange(currentPage - 1);
    }
  };

  const handleNext = () => {
    if (currentPage < totalPages) {
      onPageChange(currentPage + 1);
    }
  };

  // Basic page numbers logic (can be enhanced for more complex scenarios)
  const pageNumbers = [];
  const maxPagesToShow = 5; // Max number of page buttons to show
  let startPage = Math.max(1, currentPage - Math.floor(maxPagesToShow / 2));
  let endPage = Math.min(totalPages, startPage + maxPagesToShow - 1);

  if (endPage - startPage + 1 < maxPagesToShow) {
    startPage = Math.max(1, endPage - maxPagesToShow + 1);
  }
  
  for (let i = startPage; i <= endPage; i++) {
    pageNumbers.push(i);
  }


  return (
    <div className="mt-8 flex flex-col sm:flex-row justify-center items-center space-y-2 sm:space-y-0 sm:space-x-4">
      <div className="flex items-center space-x-2">
        <IconButton
          onClick={handlePrevious}
          disabled={currentPage === 1}
          label="Previous page"
          className="px-3 py-1.5 text-sm border border-neutral-300 dark:border-neutral-600 rounded-md bg-white dark:bg-neutral-700 text-neutral-700 dark:text-neutral-200 hover:bg-neutral-100 dark:hover:bg-neutral-600 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <ChevronLeftIcon className="w-5 h-5 mr-1" />
          Previous
        </IconButton>

        {startPage > 1 && (
           <>
            <button onClick={() => onPageChange(1)} className={`px-3 py-1.5 text-sm rounded-md ${1 === currentPage ? 'bg-primary text-white' : 'bg-white dark:bg-neutral-700 text-neutral-700 dark:text-neutral-200 hover:bg-neutral-100 dark:hover:bg-neutral-600 border border-neutral-300 dark:border-neutral-600'}`}>
                1
            </button>
            {startPage > 2 && <span className="px-2 py-1.5 text-sm text-neutral-500 dark:text-neutral-400">...</span>}
           </>
        )}

        {pageNumbers.map(number => (
          <button
            key={number}
            onClick={() => onPageChange(number)}
            className={`px-3 py-1.5 text-sm rounded-md min-w-[36px]
                        ${number === currentPage 
                          ? 'bg-primary text-white font-semibold' 
                          : 'bg-white dark:bg-neutral-700 text-neutral-700 dark:text-neutral-200 hover:bg-neutral-100 dark:hover:bg-neutral-600 border border-neutral-300 dark:border-neutral-600'}`}
          >
            {number}
          </button>
        ))}
        
        {endPage < totalPages && (
            <>
                {endPage < totalPages -1 && <span className="px-2 py-1.5 text-sm text-neutral-500 dark:text-neutral-400">...</span>}
                <button onClick={() => onPageChange(totalPages)} className={`px-3 py-1.5 text-sm rounded-md ${totalPages === currentPage ? 'bg-primary text-white' : 'bg-white dark:bg-neutral-700 text-neutral-700 dark:text-neutral-200 hover:bg-neutral-100 dark:hover:bg-neutral-600 border border-neutral-300 dark:border-neutral-600'}`}>
                    {totalPages}
                </button>
            </>
        )}

        <IconButton
          onClick={handleNext}
          disabled={currentPage === totalPages}
          label="Next page"
          className="px-3 py-1.5 text-sm border border-neutral-300 dark:border-neutral-600 rounded-md bg-white dark:bg-neutral-700 text-neutral-700 dark:text-neutral-200 hover:bg-neutral-100 dark:hover:bg-neutral-600 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Next
          <ChevronRightIcon className="w-5 h-5 ml-1" />
        </IconButton>
      </div>
      <span className="text-sm text-neutral-600 dark:text-neutral-400">
        Page {currentPage} of {totalPages}
      </span>
    </div>
  );
};

export default PaginationControls;
</file>

<file path="components/PromptForm.tsx">
import React, { useState, useEffect, useMemo } from 'react';
import { Prompt, Folder } from '../types';
// import { generateUUID } from '../utils/uuid'; // Not used here, ID generation is in App.tsx
import TagInput from './TagInput';
import { DEFAULT_FOLDER_NAME } from '../constants';
import { buildFolderHierarchy, getFlattenedFolders } from '../utils/folderUtils';


interface PromptFormProps {
  promptToEdit?: Prompt | null; 
  folders: Folder[];
  onSave: (promptData: Omit<Prompt, 'id' | 'originalPromptId' | 'version' | 'createdAt' | 'lastUsedAt' | 'timesUsed'>, existingPrompt?: Prompt | null) => void;
  onClose: () => void;
  defaultFolderId: string | null;
}

const PromptForm: React.FC<PromptFormProps> = ({ promptToEdit, folders, onSave, onClose, defaultFolderId }) => {
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [promptText, setPromptText] = useState('');
  const [tags, setTags] = useState<string[]>([]);
  const [folderId, setFolderId] = useState<string | null>(null);

  const hierarchicalFoldersForSelect = useMemo(() => {
    // Filter out the default "Uncategorized" folder before building hierarchy for user-selectable folders
    const userFolders = folders.filter(f => f.id !== defaultFolderId); 
    const hierarchy = buildFolderHierarchy(userFolders);
    return getFlattenedFolders(hierarchy);
  }, [folders, defaultFolderId]);
  
  // The default folder option should always be available and represent the "Uncategorized" folder
  const defaultFolderOption = useMemo(() => folders.find(f => f.id === defaultFolderId), [folders, defaultFolderId]);


  useEffect(() => {
    if (promptToEdit) {
      setTitle(promptToEdit.title);
      setDescription(promptToEdit.description);
      setPromptText(promptToEdit.promptText);
      setTags(promptToEdit.tags);
      setFolderId(promptToEdit.folderId); // This could be defaultFolderId or a user folder
    } else {
      // For new prompts, default to the "Uncategorized" folder
      setFolderId(defaultFolderId); 
    }
  }, [promptToEdit, defaultFolderId]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!title.trim() || !promptText.trim()) {
      alert("Title and Prompt Text are required.");
      return;
    }

    const promptData = {
      title: title.trim(),
      description: description.trim(),
      promptText: promptText.trim(),
      tags,
      folderId: folderId, // This will be defaultFolderId if "Uncategorized" is selected or if it's a new prompt
    };
    onSave(promptData, promptToEdit);
  };
  
  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div>
        <label htmlFor="title" className="block text-sm font-medium text-neutral-700 dark:text-neutral-300">
          Title
        </label>
        <input
          type="text"
          id="title"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          required
          className="mt-1 block w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm bg-white dark:bg-neutral-700 text-neutral-900 dark:text-neutral-100"
        />
      </div>

      <div>
        <label htmlFor="description" className="block text-sm font-medium text-neutral-700 dark:text-neutral-300">
          Short Description (Optional)
        </label>
        <textarea
          id="description"
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          rows={2}
          className="mt-1 block w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm bg-white dark:bg-neutral-700 text-neutral-900 dark:text-neutral-100"
        />
      </div>

      <div>
        <label htmlFor="promptText" className="block text-sm font-medium text-neutral-700 dark:text-neutral-300">
          Prompt Text
          <span className="text-xs text-neutral-500 dark:text-neutral-400 ml-1">(Use &#123;&#123;variable_name&#125;&#125; for templates)</span>
        </label>
        <textarea
          id="promptText"
          value={promptText}
          onChange={(e) => setPromptText(e.target.value)}
          rows={6}
          required
          className="mt-1 block w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm bg-white dark:bg-neutral-700 text-neutral-900 dark:text-neutral-100"
          placeholder="Enter your AI prompt. Example: Write a blog post about {{topic}}."
        />
      </div>
      
      <TagInput tags={tags} onTagsChange={setTags} />

      <div>
        <label htmlFor="folder" className="block text-sm font-medium text-neutral-700 dark:text-neutral-300">
          Folder
        </label>
        <select
          id="folder"
          value={folderId || ''} // Ensure value is correctly handled if folderId is null (should be defaultFolderId's value)
          onChange={(e) => setFolderId(e.target.value || null )} // If '' selected, treat as default/uncategorized
          className="mt-1 block w-full px-3 py-2 border border-neutral-300 dark:border-neutral-600 bg-white dark:bg-neutral-700 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm text-neutral-900 dark:text-neutral-100"
        >
          {defaultFolderOption && <option value={defaultFolderOption.id}>{DEFAULT_FOLDER_NAME}</option>}
          {/* If somehow defaultFolderOption is not available but defaultFolderId is, provide a fallback.
              This case should ideally not happen if defaultFolderId is always set from a valid folder. */}
          {!defaultFolderOption && defaultFolderId && <option value={defaultFolderId}>{DEFAULT_FOLDER_NAME} (Default)</option>} 
          
          {hierarchicalFoldersForSelect.map(folder => (
            // The style for indentation might need adjustment based on final CSS processing of Tailwind classes for option
            <option key={folder.id} value={folder.id} style={{ paddingLeft: `${(folder.level || 0) * 10 + 5}px` }}>
              {folder.name}
            </option>
          ))}
        </select>
      </div>

      <div className="flex justify-end space-x-3 pt-2">
        <button
          type="button"
          onClick={onClose}
          className="px-4 py-2 text-sm font-medium text-neutral-700 dark:text-neutral-200 bg-neutral-100 dark:bg-neutral-600 border border-neutral-300 dark:border-neutral-500 rounded-md shadow-sm hover:bg-neutral-200 dark:hover:bg-neutral-500 focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-neutral-800 focus:ring-primary-light"
        >
          Cancel
        </button>
        <button
          type="submit"
          className="px-4 py-2 text-sm font-medium text-white bg-primary hover:bg-primary-dark border border-transparent rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-neutral-800 focus:ring-primary"
        >
          {promptToEdit ? 'Save as New Version' : 'Create Prompt'}
        </button>
      </div>
    </form>
  );
};

export default PromptForm;
</file>

<file path="components/PromptTile.tsx">
import React, { useState } from 'react';
import { Prompt } from '../types';
import { formatDate } from '../utils/dateFormatter';
import IconButton from './IconButton';
import ClipboardIcon from './icons/ClipboardIcon';
import PencilIcon from './icons/PencilIcon';
import TrashIcon from './icons/TrashIcon';
import TagIcon from './icons/TagIcon';
import HistoryIcon from './icons/HistoryIcon'; 
import SparklesIcon from './icons/SparklesIcon'; // New icon for AI generation
import { extractVariables } from '../utils/templateUtils'; 

interface PromptTileProps {
  prompt: Prompt; 
  onCopy: (prompt: Prompt) => void; 
  onEdit: (prompt: Prompt) => void; 
  onDelete: (originalPromptId: string) => void; 
  onViewHistory: (originalPromptId: string) => void; 
  onGenerateWithAI: (prompt: Prompt) => void; // New prop for AI generation
  hasMultipleVersions: boolean;
}

const PromptTile: React.FC<PromptTileProps> = ({ prompt, onCopy, onEdit, onDelete, onViewHistory, onGenerateWithAI, hasMultipleVersions }) => {
  const [copiedDirectly, setCopiedDirectly] = useState(false); 

  const variables = extractVariables(prompt.promptText);
  const isTemplate = variables.length > 0;

  const handleAction = () => {
    if (isTemplate) {
      onCopy(prompt); 
    } else {
      navigator.clipboard.writeText(prompt.promptText).then(() => {
        onCopy(prompt); 
        setCopiedDirectly(true);
        setTimeout(() => setCopiedDirectly(false), 2000);
      }).catch(err => {
        console.error('Failed to copy text: ', err);
        alert('Failed to copy text.');
      });
    }
  };

  const buttonText = isTemplate 
    ? "Use Template" 
    : copiedDirectly 
      ? "Copied!" 
      : "Copy Prompt";

  return (
    <div className="bg-white dark:bg-neutral-800 shadow-lg rounded-xl p-6 hover:shadow-xl dark:hover:shadow-neutral-700/50 transition-all duration-300 ease-in-out flex flex-col justify-between h-full border border-neutral-200 dark:border-neutral-700">
      <div>
        <div className="flex justify-between items-start mb-2">
          <h3 className="text-xl font-semibold text-primary-dark dark:text-primary-light truncate mr-2" title={prompt.title}>{prompt.title}</h3>
          <div className="flex items-center space-x-1">
            {hasMultipleVersions && (
              <IconButton onClick={() => onViewHistory(prompt.originalPromptId)} label="View prompt history" className="text-neutral-500 dark:text-neutral-400 hover:text-secondary dark:hover:text-secondary p-1">
                <HistoryIcon className="w-5 h-5" />
              </IconButton>
            )}
            {/* AI Generation Button - always shown */}
            <IconButton 
              onClick={() => onGenerateWithAI(prompt)} 
              label="Generate with AI" 
              className="text-neutral-500 dark:text-neutral-400 hover:text-purple-600 dark:hover:text-purple-400 p-1"
              title="Generate with AI"
            >
              <SparklesIcon className="w-5 h-5" />
            </IconButton>
          </div>
        </div>
         <p className="text-xs text-neutral-500 dark:text-neutral-400 mb-1">Version: {prompt.version}</p>
        <p className="text-sm text-neutral-600 dark:text-neutral-300 mb-3 h-10 overflow-hidden text-ellipsis" title={prompt.description}>
          {prompt.description || "No description provided."}
        </p>
        
        {prompt.tags && prompt.tags.length > 0 && (
          <div className="mb-4 flex flex-wrap gap-2 items-center">
            <TagIcon className="w-4 h-4 text-neutral-500 dark:text-neutral-400" />
            {prompt.tags.slice(0, 3).map(tag => (
              <span key={tag} className="px-2 py-0.5 bg-secondary text-white text-xs rounded-full">
                {tag}
              </span>
            ))}
            {prompt.tags.length > 3 && (
              <span className="text-xs text-neutral-500 dark:text-neutral-400">+{prompt.tags.length - 3} more</span>
            )}
          </div>
        )}
      </div>

      <div className="mt-auto">
        <div className="text-xs text-neutral-500 dark:text-neutral-400 mb-3 space-y-1">
          <p>Used: <span className="font-medium text-neutral-700 dark:text-neutral-200">{prompt.timesUsed} times</span></p>
          <p>Last Used: <span className="font-medium text-neutral-700 dark:text-neutral-200">{formatDate(prompt.lastUsedAt)}</span></p>
          <p>Version Created: <span className="font-medium text-neutral-700 dark:text-neutral-200">{formatDate(prompt.createdAt)}</span></p>
        </div>

        <div className="flex items-center justify-between space-x-2 border-t pt-4 mt-4 border-neutral-200 dark:border-neutral-700">
          <button
            onClick={handleAction}
            className={`flex items-center justify-center w-full px-4 py-2 text-sm font-medium rounded-md shadow-sm transition-colors duration-150
                        ${(copiedDirectly && !isTemplate) ? 'bg-green-500 hover:bg-green-600 text-white' : 'bg-primary hover:bg-primary-dark text-white focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-neutral-800 focus:ring-primary-light'}`}
          >
            <ClipboardIcon className="w-5 h-5 mr-2" />
            {buttonText}
          </button>
          <div className="flex">
            <IconButton onClick={() => onEdit(prompt)} label="Edit prompt (creates new version)" className="text-neutral-600 dark:text-neutral-300 hover:text-primary dark:hover:text-primary-light">
              <PencilIcon className="w-5 h-5" />
            </IconButton>
            <IconButton onClick={() => onDelete(prompt.originalPromptId)} label="Delete prompt (all versions)" className="text-neutral-600 dark:text-neutral-300 hover:text-red-600 dark:hover:text-red-500">
              <TrashIcon className="w-5 h-5" />
            </IconButton>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PromptTile;
</file>

<file path="components/Sidebar.tsx">
import React, { useState, useMemo } from 'react';
import { Folder, HierarchicalFolder, HierarchicalTag } from '../types';
import { ALL_PROMPTS_FOLDER_ID, DEFAULT_FOLDER_NAME } from '../constants';
import FolderIcon from './icons/FolderIcon';
import PlusIcon from './icons/PlusIcon';
import PencilIcon from './icons/PencilIcon';
import TrashIcon from './icons/TrashIcon';
import ChevronRightIcon from './icons/ChevronRightIcon';
import ChevronDownIcon from './icons/ChevronDownIcon';
import IconButton from './IconButton';
import { getFlattenedFolders } from '../utils/folderUtils';
import TagIcon from './icons/TagIcon';

type SidebarView = 'folders' | 'tags';

interface FolderItemProps {
  folder: HierarchicalFolder;
  selectedFolderId: string | null;
  onSelectFolder: (folderId: string | null) => void;
  onRenameFolder: (folderId: string, newName: string) => void;
  onDeleteFolder: (folderId: string) => void;
  isExpanded: boolean;
  onToggleExpand: (folderId: string) => void;
  level: number;
  promptsInFolderBranchEmpty: boolean;
}

const FolderItem: React.FC<FolderItemProps> = ({
  folder,
  selectedFolderId,
  onSelectFolder,
  onRenameFolder,
  onDeleteFolder,
  isExpanded,
  onToggleExpand,
  level,
  promptsInFolderBranchEmpty
}) => {
  const [isRenaming, setIsRenaming] = useState(false);
  const [renameValue, setRenameValue] = useState(folder.name);

  const handleRename = () => {
    if (renameValue.trim() && renameValue.trim() !== folder.name) {
      onRenameFolder(folder.id, renameValue.trim());
    }
    setIsRenaming(false);
  };

  const canDelete = folder.isDeletable && promptsInFolderBranchEmpty;

  return (
    <div style={{ paddingLeft: `${level * 1.25}rem` }}>
      <div
        className={`group w-full flex items-center justify-between px-3 py-2 text-sm font-medium rounded-md transition-colors
                    ${selectedFolderId === folder.id 
                      ? 'bg-primary-light text-white' 
                      : 'text-neutral-700 dark:text-neutral-200 hover:bg-neutral-200 dark:hover:bg-neutral-700 hover:text-neutral-900 dark:hover:text-neutral-50'}`}
      >
        <button onClick={() => onSelectFolder(folder.id)} className="flex items-center flex-grow truncate mr-2">
          {folder.children.length > 0 && (
            <IconButton
              onClick={(e) => { e.stopPropagation(); onToggleExpand(folder.id); }}
              className={`mr-1 p-0.5 ${selectedFolderId === folder.id 
                ? 'text-white hover:bg-primary dark:hover:bg-primary-dark' 
                : 'text-neutral-500 dark:text-neutral-400 hover:bg-neutral-300 dark:hover:bg-neutral-600'}`}
              label={isExpanded ? "Collapse" : "Expand"}
            >
              {isExpanded ? <ChevronDownIcon className="w-4 h-4" /> : <ChevronRightIcon className="w-4 h-4" />}
            </IconButton>
          )}
          <FolderIcon className={`w-5 h-5 mr-2 ${folder.children.length === 0 && level > 0 ? 'ml-[1.125rem]' : folder.children.length === 0 && level === 0 ? 'ml-[1.125rem]' : ''}  
                               ${selectedFolderId === folder.id ? 'text-white' : 'text-neutral-500 dark:text-neutral-400'}`} />
          {isRenaming ? (
            <input
              type="text"
              value={renameValue}
              onChange={(e) => setRenameValue(e.target.value)}
              onBlur={handleRename}
              onKeyPress={(e) => e.key === 'Enter' && handleRename()}
              onClick={(e) => e.stopPropagation()}
              className="text-sm bg-transparent border border-primary-light dark:border-primary rounded px-1 py-0.5 w-full text-neutral-800 dark:text-neutral-100 dark:bg-neutral-600"
              autoFocus
            />
          ) : (
            <span className="truncate">{folder.name}</span>
          )}
        </button>
        {!isRenaming && (
          <div className="opacity-0 group-hover:opacity-100 transition-opacity flex items-center">
            {folder.isRenamable && (
              <IconButton
                onClick={(e) => { e.stopPropagation(); setIsRenaming(true); }}
                label="Rename folder"
                className={`p-1 ${selectedFolderId === folder.id 
                  ? 'text-white hover:bg-primary dark:hover:bg-primary-dark' 
                  : 'text-neutral-500 dark:text-neutral-400 hover:text-primary-dark dark:hover:text-primary-light'}`}
              >
                <PencilIcon className="w-4 h-4" />
              </IconButton>
            )}
            {folder.isDeletable && (
              <IconButton
                onClick={(e) => { e.stopPropagation(); onDeleteFolder(folder.id); }}
                label="Delete folder"
                disabled={!canDelete}
                className={`p-1 ${selectedFolderId === folder.id 
                  ? 'text-white hover:bg-red-400 dark:hover:bg-red-500' 
                  : 'text-neutral-500 dark:text-neutral-400 hover:text-red-600 dark:hover:text-red-500'} 
                  ${!canDelete ? 'opacity-50 cursor-not-allowed' : ''}`}
                title={!canDelete ? "Folder and all subfolders must be empty of prompts to delete." : "Delete folder"}
              >
                <TrashIcon className="w-4 h-4" />
              </IconButton>
            )}
          </div>
        )}
      </div>
      {isExpanded && folder.children.length > 0 && (
        <div className="mt-1 space-y-1">
          {/* Recursive rendering handled by parent in renderFolderItems */}
        </div>
      )}
    </div>
  );
};

interface TagItemProps {
    tag: HierarchicalTag;
    selectedTag: string | null;
    onSelectTag: (tag: string | null) => void;
    isExpanded: boolean;
    onToggleExpand: (fullName: string) => void;
    level: number;
}

const TagItem: React.FC<TagItemProps> = ({ tag, selectedTag, onSelectTag, isExpanded, onToggleExpand, level }) => {
    const isSelected = selectedTag === tag.fullName;
    return (
        <div style={{ paddingLeft: `${level * 1.25}rem` }}>
            <div
                className={`group w-full flex items-center justify-between px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                    isSelected
                        ? 'bg-primary-light text-white'
                        : 'text-neutral-700 dark:text-neutral-200 hover:bg-neutral-200 dark:hover:bg-neutral-700 hover:text-neutral-900 dark:hover:text-neutral-50'
                }`}
            >
                <button onClick={() => onSelectTag(tag.fullName)} className="flex items-center flex-grow truncate mr-2">
                    {tag.children.length > 0 && (
                        <IconButton
                            onClick={(e) => { e.stopPropagation(); onToggleExpand(tag.fullName); }}
                            className={`mr-1 p-0.5 ${isSelected ? 'text-white hover:bg-primary dark:hover:bg-primary-dark' : 'text-neutral-500 dark:text-neutral-400 hover:bg-neutral-300 dark:hover:bg-neutral-600'}`}
                            label={isExpanded ? "Collapse" : "Expand"}
                        >
                            {isExpanded ? <ChevronDownIcon className="w-4 h-4" /> : <ChevronRightIcon className="w-4 h-4" />}
                        </IconButton>
                    )}
                    <TagIcon className={`w-5 h-5 mr-2 ${tag.children.length === 0 ? 'ml-[1.125rem]' : ''} ${isSelected ? 'text-white' : 'text-neutral-500 dark:text-neutral-400'}`} />
                    <span className="truncate">{tag.name}</span>
                </button>
            </div>
            {isExpanded && tag.children.length > 0 && (
                <div className="mt-1 space-y-1">
                    {/* Recursive rendering handled by parent in renderTagItems */}
                </div>
            )}
        </div>
    );
};

interface SidebarProps {
  hierarchicalFolders: HierarchicalFolder[]; 
  allFolders: Folder[]; 
  selectedFolderId: string | null;
  onSelectFolder: (folderId: string | null) => void;
  onAddFolder: (folderName: string, parentId: string | null) => void;
  onRenameFolder: (folderId: string, newName: string) => void;
  onDeleteFolder: (folderId: string) => void;
  isFolderBranchEmpty: (folderId: string) => boolean;
  hierarchicalTags: HierarchicalTag[];
  selectedTag: string | null;
  onSelectTag: (tag: string | null) => void;
}

const Sidebar: React.FC<SidebarProps> = ({
  hierarchicalFolders, 
  allFolders, 
  selectedFolderId,
  onSelectFolder,
  onAddFolder,
  onRenameFolder,
  onDeleteFolder,
  isFolderBranchEmpty,
  hierarchicalTags,
  selectedTag,
  onSelectTag,
}) => {
  const [newFolderName, setNewFolderName] = useState('');
  const [newFolderParentId, setNewFolderParentId] = useState<string | null>(null);
  const [showAddFolderInput, setShowAddFolderInput] = useState(false);
  const [expandedFolders, setExpandedFolders] = useState<Record<string, boolean>>({});
  const [expandedTags, setExpandedTags] = useState<Record<string, boolean>>({});
  const [sidebarView, setSidebarView] = useState<SidebarView>('folders');

  const defaultFolderForDisplay = useMemo(() => allFolders.find(f => f.name === DEFAULT_FOLDER_NAME && f.parentId === null), [allFolders]);

  const flattenedFoldersForParentSelect = useMemo(() => {
    return getFlattenedFolders(hierarchicalFolders); 
  }, [hierarchicalFolders]);

  const handleToggleFolderExpand = (folderId: string) => {
    setExpandedFolders(prev => ({ ...prev, [folderId]: !prev[folderId] }));
  };

  const handleToggleTagExpand = (fullName: string) => {
    setExpandedTags(prev => ({ ...prev, [fullName]: !prev[fullName] }));
  };

  const handleAddFolderSubmit = () => {
    if (newFolderName.trim() === '') return;
     const siblingFolders = allFolders.filter(f => f.parentId === newFolderParentId);
    if (siblingFolders.find(f => f.name.toLowerCase() === newFolderName.trim().toLowerCase())) {
        alert(`A folder named "${newFolderName.trim()}" already exists in this location.`);
        return;
    }
    onAddFolder(newFolderName.trim(), newFolderParentId);
    setNewFolderName('');
    setNewFolderParentId(null);
    setShowAddFolderInput(false);
  };
  
  const renderFolderItems = (foldersToRender: HierarchicalFolder[], level: number) => {
    return foldersToRender.map(folder => (
      <React.Fragment key={folder.id}>
        <FolderItem
          folder={folder}
          selectedFolderId={selectedFolderId}
          onSelectFolder={onSelectFolder}
          onRenameFolder={onRenameFolder}
          onDeleteFolder={onDeleteFolder}
          isExpanded={!!expandedFolders[folder.id]}
          onToggleExpand={handleToggleFolderExpand}
          level={level}
          promptsInFolderBranchEmpty={isFolderBranchEmpty(folder.id)}
        />
        {expandedFolders[folder.id] && folder.children && folder.children.length > 0 && (
          <div className="pl-0 mt-1 space-y-1">
            {renderFolderItems(folder.children, level + 1)}
          </div>
        )}
      </React.Fragment>
    ));
  };

  const renderTagItems = (tagsToRender: HierarchicalTag[], level: number) => {
    return tagsToRender.map(tag => (
      <React.Fragment key={tag.fullName}>
        <TagItem
          tag={tag}
          selectedTag={selectedTag}
          onSelectTag={onSelectTag}
          isExpanded={!!expandedTags[tag.fullName]}
          onToggleExpand={handleToggleTagExpand}
          level={level}
        />
        {expandedTags[tag.fullName] && tag.children && tag.children.length > 0 && (
          <div className="pl-0 mt-1 space-y-1">
            {renderTagItems(tag.children, level + 1)}
          </div>
        )}
      </React.Fragment>
    ));
  };

  return (
    <aside className="w-72 bg-neutral-50 dark:bg-neutral-800 p-4 border-r border-neutral-200 dark:border-neutral-700 space-y-6 h-full overflow-y-auto flex flex-col transition-colors duration-300">
      <div className="flex-grow">
        <div className="flex justify-center mb-4">
          <div className="p-1 bg-neutral-200 dark:bg-neutral-700 rounded-lg flex">
            <button
              onClick={() => setSidebarView('folders')}
              className={`px-4 py-1 text-sm font-medium rounded-md ${sidebarView === 'folders' ? 'bg-white dark:bg-neutral-600 text-primary-dark dark:text-white' : 'text-neutral-600 dark:text-neutral-300'}`}
            >
              Folders
            </button>
            <button
              onClick={() => setSidebarView('tags')}
              className={`px-4 py-1 text-sm font-medium rounded-md ${sidebarView === 'tags' ? 'bg-white dark:bg-neutral-600 text-primary-dark dark:text-white' : 'text-neutral-600 dark:text-neutral-300'}`}
            >
              Tags
            </button>
          </div>
        </div>
        <h2 className="text-xs font-semibold text-neutral-500 dark:text-neutral-400 uppercase tracking-wider mb-3 px-3">
          {sidebarView === 'folders' ? 'Folders' : 'Tags'}
        </h2>
        <nav className="space-y-1">
          {sidebarView === 'folders' ? (
            <>
              <button
                onClick={() => onSelectFolder(ALL_PROMPTS_FOLDER_ID)}
                className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors
                            ${selectedFolderId === ALL_PROMPTS_FOLDER_ID 
                              ? 'bg-primary-light text-white' 
                              : 'text-neutral-700 dark:text-neutral-200 hover:bg-neutral-200 dark:hover:bg-neutral-700 hover:text-neutral-900 dark:hover:text-neutral-50'}`}
              >
                <FolderIcon className={`w-5 h-5 mr-3 ${selectedFolderId === ALL_PROMPTS_FOLDER_ID ? 'text-white' : 'text-neutral-500 dark:text-neutral-400'}`} />
                All Prompts
              </button>
              {defaultFolderForDisplay && ( 
                <button
                  onClick={() => onSelectFolder(null)} 
                  className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors
                              ${selectedFolderId === null 
                                ? 'bg-primary-light text-white' 
                                : 'text-neutral-700 dark:text-neutral-200 hover:bg-neutral-200 dark:hover:bg-neutral-700 hover:text-neutral-900 dark:hover:text-neutral-50'}`}
                >
                  <FolderIcon className={`w-5 h-5 mr-3 ${selectedFolderId === null ? 'text-white' : 'text-neutral-500 dark:text-neutral-400'}`} />
                  {DEFAULT_FOLDER_NAME}
                </button>
              )}
              {renderFolderItems(hierarchicalFolders, 0)}
            </>
          ) : (
            <>
              <button
                onClick={() => onSelectTag(null)}
                className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors
                            ${selectedTag === null 
                              ? 'bg-primary-light text-white' 
                              : 'text-neutral-700 dark:text-neutral-200 hover:bg-neutral-200 dark:hover:bg-neutral-700 hover:text-neutral-900 dark:hover:text-neutral-50'}`}
              >
                <TagIcon className={`w-5 h-5 mr-3 ${selectedTag === null ? 'text-white' : 'text-neutral-500 dark:text-neutral-400'}`} />
                All Tags
              </button>
              {renderTagItems(hierarchicalTags, 0)}
            </>
          )}
        </nav>
      </div>

      {sidebarView === 'folders' && (
        <div className="mt-auto pt-4 border-t border-neutral-200 dark:border-neutral-700">
          {showAddFolderInput ? (
            <div className="space-y-2 p-2 bg-neutral-100 dark:bg-neutral-700 rounded-md">
              <input
                type="text"
                value={newFolderName}
                onChange={(e) => setNewFolderName(e.target.value)}
                placeholder="New folder name"
                className="w-full px-3 py-2 text-sm border border-neutral-300 dark:border-neutral-500 rounded-md focus:ring-primary focus:border-primary bg-white dark:bg-neutral-600 text-neutral-900 dark:text-neutral-50"
              />
              <select
                value={newFolderParentId || ''}
                onChange={(e) => setNewFolderParentId(e.target.value || null)}
                className="w-full px-3 py-2 text-sm border border-neutral-300 dark:border-neutral-500 bg-white dark:bg-neutral-600 rounded-md focus:ring-primary focus:border-primary text-neutral-900 dark:text-neutral-50"
              >
                <option value="" className="text-neutral-900 dark:text-neutral-50">Root Level</option>
                {flattenedFoldersForParentSelect.map(folder => (
                  <option key={folder.id} value={folder.id} className="text-neutral-900 dark:text-neutral-50">{folder.name}</option>
                ))}
              </select>
              <div className="flex space-x-2">
                <button
                  onClick={handleAddFolderSubmit}
                  className="flex-1 px-3 py-2 text-sm font-medium text-white bg-primary hover:bg-primary-dark rounded-md"
                >
                  Add Folder
                </button>
                <button
                  onClick={() => { setShowAddFolderInput(false); setNewFolderName(''); setNewFolderParentId(null); }}
                  className="flex-1 px-3 py-2 text-sm font-medium text-neutral-700 dark:text-neutral-100 bg-neutral-200 dark:bg-neutral-500 hover:bg-neutral-300 dark:hover:bg-neutral-400 rounded-md"
                >
                  Cancel
                </button>
              </div>
            </div>
          ) : (
            <button
              onClick={() => setShowAddFolderInput(true)}
              className="w-full flex items-center justify-center px-3 py-2 text-sm font-medium text-primary dark:text-primary-light hover:bg-primary-light hover:text-white dark:hover:bg-primary-dark dark:hover:text-white border-2 border-dashed border-primary-light dark:border-primary rounded-md transition-colors"
              aria-label="Add new folder"
            >
              <PlusIcon className="w-5 h-5 mr-2" />
              Add Folder
            </button>
          )}
        </div>
      )}
    </aside>
  );
};

export default Sidebar;
</file>

<file path="components/TagInput.tsx">
import React, { useState } from 'react';

interface TagInputProps {
  tags: string[];
  onTagsChange: (tags: string[]) => void;
  label?: string;
}

const TagInput: React.FC<TagInputProps> = ({ tags, onTagsChange, label = "Tags" }) => {
  const [inputValue, setInputValue] = useState('');

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };

  const handleInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if ((e.key === 'Enter' || e.key === ',') && inputValue.trim() !== '') {
      e.preventDefault();
      const newTag = inputValue.trim();
      if (!tags.includes(newTag)) {
        onTagsChange([...tags, newTag]);
      }
      setInputValue('');
    } else if (e.key === 'Backspace' && inputValue === '' && tags.length > 0) {
      onTagsChange(tags.slice(0, -1));
    }
  };

  const removeTag = (tagToRemove: string) => {
    onTagsChange(tags.filter(tag => tag !== tagToRemove));
  };

  return (
    <div>
      <label htmlFor="tag-input" className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1">
        {label} (comma or Enter to add)
      </label>
      <div className="flex flex-wrap items-center gap-2 p-2 border border-neutral-300 dark:border-neutral-600 rounded-md shadow-sm focus-within:ring-1 focus-within:ring-primary focus-within:border-primary bg-white dark:bg-neutral-700">
        {tags.map(tag => (
          <span key={tag} className="flex items-center gap-1 px-2 py-1 bg-primary-light text-white text-xs font-medium rounded-full">
            {tag}
            <button
              type="button"
              onClick={() => removeTag(tag)}
              className="ml-1 text-primary-dark hover:text-white" /* Consider dark mode for this button text if needed */
              aria-label={`Remove ${tag}`}
            >
              &times;
            </button>
          </span>
        ))}
        <input
          id="tag-input"
          type="text"
          value={inputValue}
          onChange={handleInputChange}
          onKeyDown={handleInputKeyDown}
          className="flex-grow p-1 border-none outline-none focus:ring-0 text-sm bg-transparent text-neutral-900 dark:text-neutral-100 placeholder-neutral-400 dark:placeholder-neutral-500"
          placeholder={tags.length === 0 ? "Add tags..." : ""}
        />
      </div>
    </div>
  );
};

export default TagInput;
</file>

<file path="components/ThemeToggleButton.tsx">
import React from 'react';
import useTheme from '../hooks/useTheme';
import SunIcon from './icons/SunIcon';
import MoonIcon from './icons/MoonIcon';
import IconButton from './IconButton';

const ThemeToggleButton: React.FC = () => {
  const [theme, toggleTheme] = useTheme();

  return (
    <IconButton
      onClick={toggleTheme}
      label={theme === 'light' ? 'Switch to dark mode' : 'Switch to light mode'}
      className="text-neutral-600 dark:text-neutral-300 hover:text-primary dark:hover:text-primary-light"
    >
      {theme === 'light' ? (
        <MoonIcon className="w-6 h-6" />
      ) : (
        <SunIcon className="w-6 h-6" />
      )}
    </IconButton>
  );
};

export default ThemeToggleButton;
</file>

<file path="components/VersionHistoryModal.tsx">
import React, { useState } from 'react';
import { Prompt } from '../types';
import { formatDate } from '../utils/dateFormatter';
import Modal from './Modal';
import IconButton from './IconButton';
import ClipboardIcon from './icons/ClipboardIcon';

interface VersionHistoryModalProps {
  isOpen: boolean;
  onClose: () => void;
  promptVersions: Prompt[]; // All versions of a single prompt, sorted newest first ideally
  originalPromptTitle: string;
}

const VersionHistoryModal: React.FC<VersionHistoryModalProps> = ({ isOpen, onClose, promptVersions, originalPromptTitle }) => {
  const [copiedVersionId, setCopiedVersionId] = useState<string | null>(null);

  if (!isOpen) return null;

  const handleCopyVersionText = (textToCopy: string, versionId: string) => {
    navigator.clipboard.writeText(textToCopy).then(() => {
      setCopiedVersionId(versionId);
      setTimeout(() => setCopiedVersionId(null), 2000); // Reset after 2 seconds
    }).catch(err => {
      console.error('Failed to copy version text: ', err);
      alert('Failed to copy text.');
    });
  };

  // Sort versions by version number descending (newest first)
  const sortedVersions = [...promptVersions].sort((a, b) => b.version - a.version);

  return (
    <Modal isOpen={isOpen} onClose={onClose} title={`Version History for "${originalPromptTitle}"`}>
      {sortedVersions.length > 0 ? (
        <ul className="space-y-4 max-h-[60vh] overflow-y-auto">
          {sortedVersions.map((version) => (
            <li key={version.id} className="p-4 border border-neutral-200 dark:border-neutral-700 rounded-lg shadow-sm bg-neutral-50 dark:bg-neutral-800">
              <div className="flex justify-between items-center mb-2">
                <h4 className="text-lg font-semibold text-primary-dark dark:text-primary-light">Version {version.version}</h4>
                <span className="text-xs text-neutral-500 dark:text-neutral-400">Created: {formatDate(version.createdAt)}</span>
              </div>
              <p className="text-sm text-neutral-700 dark:text-neutral-200 mb-1"><strong>Title:</strong> {version.title}</p>
              {version.description && <p className="text-sm text-neutral-600 dark:text-neutral-300 mb-1"><strong>Description:</strong> {version.description}</p>}
              
              <div className="mt-2">
                <div className="flex justify-between items-center mb-1">
                    <p className="text-sm text-neutral-600 dark:text-neutral-300"><strong>Prompt Text:</strong></p>
                    <IconButton
                        onClick={() => handleCopyVersionText(version.promptText, version.id)}
                        label={copiedVersionId === version.id ? "Copied!" : "Copy prompt text"}
                        className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-primary dark:hover:text-primary-light"
                        title={copiedVersionId === version.id ? "Copied!" : "Copy prompt text"}
                    >
                        <ClipboardIcon className="w-4 h-4" />
                         {copiedVersionId === version.id && <span className="ml-1 text-xs">Copied!</span>}
                    </IconButton>
                </div>
                <pre className="text-xs bg-neutral-100 dark:bg-neutral-700 text-neutral-800 dark:text-neutral-100 p-3 rounded whitespace-pre-wrap break-words max-h-40 overflow-y-auto border border-neutral-200 dark:border-neutral-600">
                  {version.promptText}
                </pre>
              </div>

              {version.tags.length > 0 && (
                <div className="mt-2">
                  <strong className="text-sm text-neutral-600 dark:text-neutral-300">Tags:</strong>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {version.tags.map(tag => (
                      <span key={tag} className="px-2 py-0.5 bg-secondary text-white text-xs rounded-full">{tag}</span>
                    ))}
                  </div>
                </div>
              )}
               <div className="text-xs text-neutral-500 dark:text-neutral-400 mt-2">
                <span>Used: {version.timesUsed} times</span> | <span>Last Used: {formatDate(version.lastUsedAt)}</span>
              </div>
            </li>
          ))}
        </ul>
      ) : (
        <p className="text-neutral-600 dark:text-neutral-300">No version history available for this prompt.</p>
      )}
      <div className="mt-6 flex justify-end">
        <button
          onClick={onClose}
          className="px-4 py-2 text-sm font-medium text-neutral-700 dark:text-neutral-200 bg-neutral-100 dark:bg-neutral-700 border border-neutral-300 dark:border-neutral-600 rounded-md shadow-sm hover:bg-neutral-200 dark:hover:bg-neutral-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-light dark:focus:ring-offset-neutral-800"
        >
          Close
        </button>
      </div>
    </Modal>
  );
};

export default VersionHistoryModal;
</file>

<file path="hooks/useLocalStorage.ts">
import { useState, useEffect } from 'react';

function useLocalStorage<T,>(key: string, initialValue: T): [T, React.Dispatch<React.SetStateAction<T>>] {
  const [storedValue, setStoredValue] = useState<T>(() => {
    if (typeof window === 'undefined') {
      return initialValue;
    }
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.error(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });

  useEffect(() => {
    if (typeof window === 'undefined') {
      return;
    }
    try {
      window.localStorage.setItem(key, JSON.stringify(storedValue));
    } catch (error) {
      console.error(`Error setting localStorage key "${key}":`, error);
    }
  }, [key, storedValue]);

  return [storedValue, setStoredValue];
}

export default useLocalStorage;
</file>

<file path="hooks/useTheme.ts">
import { useState, useEffect, useCallback } from 'react';

type Theme = 'light' | 'dark';

function useTheme(): [Theme, () => void] {
  const [theme, setTheme] = useState<Theme>(() => {
    if (typeof window === 'undefined') {
      return 'light'; // Default to light theme SSR
    }
    const storedTheme = window.localStorage.getItem('theme') as Theme | null;
    if (storedTheme) {
      return storedTheme;
    }
    // Check for user's OS preference
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
  });

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const root = window.document.documentElement;
    if (theme === 'dark') {
      root.classList.add('dark');
    } else {
      root.classList.remove('dark');
    }
    window.localStorage.setItem('theme', theme);
  }, [theme]);

  const toggleTheme = useCallback(() => {
    setTheme(prevTheme => (prevTheme === 'light' ? 'dark' : 'light'));
  }, []);

  return [theme, toggleTheme];
}

export default useTheme;
</file>

<file path="utils/dateFormatter.ts">
export const formatDate = (isoDateString: string | null): string => {
  if (!isoDateString) return 'N/A';
  try {
    return new Date(isoDateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  } catch (error) {
    return 'Invalid Date';
  }
};
</file>

<file path="utils/idb.ts">
import { openDB, IDBPDatabase, IDBPTransaction } from 'idb';
import { Prompt, Folder, AIPMPRDB, ExportData } from '../types';
import { generateUUID } from './uuid';
import { DEFAULT_FOLDER_NAME } from '../constants';

const DB_NAME = 'AIPromptManagerDB';
const DB_VERSION = 1;
const PROMPTS_STORE_NAME = 'prompts';
const FOLDERS_STORE_NAME = 'folders';

let dbPromise: Promise<IDBPDatabase<AIPMPRDB>> | null = null;

const getDB = (): Promise<IDBPDatabase<AIPMPRDB>> => {
  if (!dbPromise) {
    dbPromise = openDB<AIPMPRDB>(DB_NAME, DB_VERSION, {
      upgrade(db: IDBPDatabase<AIPMPRDB>, oldVersion: number, newVersion: number | null, tx: IDBPTransaction<AIPMPRDB, (typeof PROMPTS_STORE_NAME | typeof FOLDERS_STORE_NAME)[], "versionchange">, event: IDBVersionChangeEvent) {
        console.log(`Upgrading DB from version ${oldVersion} to ${newVersion}`);
        if (!db.objectStoreNames.contains(PROMPTS_STORE_NAME)) {
          const promptsStore = db.createObjectStore(PROMPTS_STORE_NAME, { keyPath: 'id' });
          promptsStore.createIndex('originalPromptId', 'originalPromptId');
          promptsStore.createIndex('folderId', 'folderId');
          promptsStore.createIndex('version', 'version');
          promptsStore.createIndex('createdAt', 'createdAt');
          promptsStore.createIndex('lastUsedAt', 'lastUsedAt');
          promptsStore.createIndex('title', 'title');
        }
        if (!db.objectStoreNames.contains(FOLDERS_STORE_NAME)) {
          const foldersStore = db.createObjectStore(FOLDERS_STORE_NAME, { keyPath: 'id' });
          foldersStore.createIndex('parentId', 'parentId');
          foldersStore.createIndex('name', 'name');
        }
      },
      blocked(currentVersion: number, blockedVersion: number | null, event: IDBVersionChangeEvent) {
        console.error(`IndexedDB open blocked. Current version: ${currentVersion}, Attempted version: ${blockedVersion}. Please close other tabs running this application and refresh.`);
        alert("The application database is blocked by an older version in another tab. Please close other tabs and refresh the page.");
      },
      blocking(this: IDBPDatabase<AIPMPRDB>, currentVersion: number, blockedVersion: number | null, event: IDBVersionChangeEvent) {
        console.warn(`IndexedDB connection (version ${currentVersion}) is blocking an attempt to open version ${blockedVersion}. Closing this blocking connection.`);
        this.close(); 
        alert("The application database needs to upgrade. This tab was blocking it and has closed its database connection. Please refresh the page for the changes to take effect.");
      },
      terminated() {
        console.warn('IndexedDB connection was terminated by the browser.');
        dbPromise = null; 
      }
    }).catch(error => {
      console.error("Failed to open IndexedDB:", error);
      dbPromise = null; 
      throw error; 
    });
  }
  return dbPromise;
};


export const initDefaultFolderDB = async (): Promise<Folder> => {
  const db = await getDB();
  const tx = db.transaction(FOLDERS_STORE_NAME, 'readwrite');
  const store = tx.objectStore(FOLDERS_STORE_NAME);
  let defaultFolder = (await store.getAll()).find(f => f.name === DEFAULT_FOLDER_NAME && f.parentId === null);

  if (!defaultFolder) {
    defaultFolder = {
      id: generateUUID(),
      name: DEFAULT_FOLDER_NAME,
      parentId: null,
      isDeletable: false,
      isRenamable: false,
    };
    await store.add(defaultFolder);
  }
  await tx.done;
  return defaultFolder;
};


// Prompt Functions
export const getAllPromptsDB = async (): Promise<Prompt[]> => {
  const db = await getDB();
  return db.getAll(PROMPTS_STORE_NAME);
};

export const getPromptByIdDB = async (id: string): Promise<Prompt | undefined> => {
  const db = await getDB();
  return db.get(PROMPTS_STORE_NAME, id);
};

export const getPromptsByOriginalIdDB = async (originalPromptId: string): Promise<Prompt[]> => {
  const db = await getDB();
  return db.getAllFromIndex(PROMPTS_STORE_NAME, 'originalPromptId', originalPromptId);
};

export const addPromptDB = async (prompt: Prompt): Promise<string> => {
  const db = await getDB();
  return db.add(PROMPTS_STORE_NAME, prompt);
};

export const updatePromptDB = async (prompt: Prompt): Promise<string> => {
  const db = await getDB();
  return db.put(PROMPTS_STORE_NAME, prompt);
};

export const deletePromptsByOriginalIdDB = async (originalPromptId: string): Promise<void> => {
  const db = await getDB();
  const tx = db.transaction(PROMPTS_STORE_NAME, 'readwrite');
  const store = tx.objectStore(PROMPTS_STORE_NAME);
  const index = store.index('originalPromptId');
  let cursor = await index.openCursor(originalPromptId);
  while (cursor) {
    await store.delete(cursor.primaryKey); 
    cursor = await cursor.continue();
  }
  await tx.done;
};

export const clearAllPromptsDB = async (): Promise<void> => {
  const db = await getDB();
  await db.clear(PROMPTS_STORE_NAME);
};

// Folder Functions
export const getAllFoldersDB = async (): Promise<Folder[]> => {
  const db = await getDB();
  return db.getAll(FOLDERS_STORE_NAME);
};

export const addFolderDB = async (folder: Folder): Promise<string> => {
  const db = await getDB();
  return db.add(FOLDERS_STORE_NAME, folder);
};

export const updateFolderDB = async (folder: Folder): Promise<string> => {
  const db = await getDB();
  return db.put(FOLDERS_STORE_NAME, folder);
};

export const deleteFolderDB = async (folderId: string): Promise<void> => {
  await deleteFoldersDB([folderId]);
};

export const deleteFoldersDB = async (folderIds: string[]): Promise<void> => {
  const db = await getDB();
  const tx = db.transaction(FOLDERS_STORE_NAME, 'readwrite');
  const store = tx.objectStore(FOLDERS_STORE_NAME);
  await Promise.all(folderIds.map(id => store.delete(id)));
  await tx.done;
};

export const getFolderByIdDB = async (id: string): Promise<Folder | undefined> => {
    const db = await getDB();
    return db.get(FOLDERS_STORE_NAME, id);
};

export const importDataDB = async (data: ExportData): Promise<void> => {
  const db = await getDB();
  const tx = db.transaction([PROMPTS_STORE_NAME, FOLDERS_STORE_NAME], 'readwrite');
  
  const promptsStore = tx.objectStore(PROMPTS_STORE_NAME);
  const foldersStore = tx.objectStore(FOLDERS_STORE_NAME);

  await promptsStore.clear();
  await foldersStore.clear();
  
  for (const folder of data.folders) {
    await foldersStore.add(folder);
  }
  
  for (const prompt of data.prompts) {
    await promptsStore.add(prompt);
  }

  return tx.done;
};

export const clearAllFoldersDB = async (): Promise<void> => {
  const db = await getDB();
  await db.clear(FOLDERS_STORE_NAME);
};
</file>

<file path="utils/ollamaApi.ts">
// utils/ollamaApi.ts
interface OllamaGenerateParams {
  model: string;
  prompt: string;
  stream?: boolean;
  // system?: string; // Example: For system prompt
  // template?: string; // Example: For custom prompt template
  // options?: Record<string, any>; // Example: For temperature, top_p, etc.
}

interface OllamaGenerateResponse {
  model: string;
  created_at: string;
  response: string; // This is the full generated text for non-streaming
  done: boolean;
  // Other fields like context, total_duration, eval_count, eval_duration etc. might be present
  error?: string; // Ollama might return an error in the JSON body
}

export async function generateTextWithOllama(promptText: string): Promise<string> {
  const ollamaApiEndpoint = process.env.OLLAMA_API_ENDPOINT;
  const ollamaModelName = process.env.OLLAMA_MODEL_NAME;

  if (!ollamaApiEndpoint) {
    const errorMsg = "Ollama API endpoint is not configured. Please set OLLAMA_API_ENDPOINT in your environment variables (e.g., in a .env file if your framework supports it, like VITE_OLLAMA_API_ENDPOINT for Vite).";
    console.error(errorMsg);
    alert(errorMsg);
    throw new Error(errorMsg);
  }

  if (!ollamaModelName) {
    const errorMsg = "Ollama model name is not configured. Please set OLLAMA_MODEL_NAME in your environment variables (e.g., OLLAMA_MODEL_NAME or VITE_OLLAMA_MODEL_NAME for Vite).";
    console.error(errorMsg);
    alert(errorMsg);
    throw new Error(errorMsg);
  }

  const payload: OllamaGenerateParams = {
    model: ollamaModelName,
    prompt: promptText,
    stream: false, // Using non-streaming response for simplicity
  };

  try {
    const fetchResponse = await fetch(ollamaApiEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });

    if (!fetchResponse.ok) {
      // Attempt to parse error from Ollama's response body
      let errorDetail = `Error communicating with Ollama: HTTP ${fetchResponse.status} - ${fetchResponse.statusText}. Endpoint: ${ollamaApiEndpoint}`;
      try {
        const errorJson: OllamaGenerateResponse = await fetchResponse.json();
        if (errorJson.error) {
          errorDetail = `Ollama API Error: ${errorJson.error} (Status: ${fetchResponse.status})`;
          // Specifically check for model not found error
          if (errorJson.error.toLowerCase().includes("model") && (errorJson.error.toLowerCase().includes("not found") || errorJson.error.toLowerCase().includes("does not exist"))) {
            errorDetail = `Ollama model '${ollamaModelName}' not found. Please ensure the model is pulled/available in Ollama and the OLLAMA_MODEL_NAME is correct. (Details: ${errorJson.error})`;
          }
        }
      } catch (e) {
        // Could not parse JSON error, stick with HTTP status based error.
        // This might happen if Ollama is down and a proxy/gateway returns non-JSON error page.
        console.warn("Could not parse JSON error from Ollama error response:", e);
      }
      console.error(errorDetail);
      alert(errorDetail);
      throw new Error(errorDetail);
    }

    const data: OllamaGenerateResponse = await fetchResponse.json();

    // Some Ollama versions might return 200 OK but still include an error in the body
    if (data.error) {
        const errorMsg = `Ollama returned an error in response: ${data.error}`;
        console.error(errorMsg);
        alert(errorMsg);
        throw new Error(errorMsg);
    }
    
    if (typeof data.response === 'string' && data.response.trim() !== '') {
      return data.response.trim();
    } else {
      const errorMsg = "Ollama API returned an empty or invalid response text. Check Ollama logs for more details.";
      console.warn(errorMsg, "Received data:", data);
      alert(errorMsg);
      throw new Error(errorMsg);
    }

  } catch (error: any) {
    // This catches:
    // 1. Network errors (fetch itself failed, e.g., Ollama server not running, DNS issue, CORS if not properly configured on Ollama server & it's a different origin)
    // 2. Errors explicitly thrown from the blocks above (e.g., !fetchResponse.ok, missing env vars)
    
    let errorMessageToAlertAndThrow: string;

    if (error instanceof Error) {
        // If the error message is one of our specific, already alerted messages, just rethrow.
        if (error.message.startsWith("Ollama API endpoint is not configured") ||
            error.message.startsWith("Ollama model name is not configured") ||
            error.message.startsWith("Ollama API Error:") ||
            error.message.startsWith("Ollama model ") || // Covers "Ollama model '...' not found"
            error.message.startsWith("Error communicating with Ollama:") ||
            error.message.startsWith("Ollama returned an error in response:") ||
            error.message.startsWith("Ollama API returned an empty or invalid response text.")) {
          throw error; // Rethrow the specific error, alert was already shown
        }
        // For other errors (likely network/fetch failures)
        errorMessageToAlertAndThrow = `Failed to connect to Ollama or process its response. Ensure Ollama is running and the endpoint '${ollamaApiEndpoint}' is correct. Details: ${error.message}`;
    } else {
        errorMessageToAlertAndThrow = `An unknown error occurred while trying to communicate with Ollama. Endpoint: ${ollamaApiEndpoint}`;
    }
    
    console.error("Overall error in generateTextWithOllama:", error); // Log the original error object
    alert(errorMessageToAlertAndThrow);
    throw new Error(errorMessageToAlertAndThrow); // Rethrow a new error or the modified one for App.tsx
  }
}
</file>

<file path="utils/templateUtils.ts">
// Extracts variables from a template string.
// Variables are expected in the format {{variable_name}}.
export const extractVariables = (templateString: string): string[] => {
  if (!templateString) return [];
  const regex = /\{\{([^{}]+?)\}\}/g;
  const matches = new Set<string>();
  let match;
  while ((match = regex.exec(templateString)) !== null) {
    matches.add(match[1].trim());
  }
  return Array.from(matches);
};

// Replaces variables in a template string with their provided values.
export const fillTemplate = (templateString: string, values: Record<string, string>): string => {
  if (!templateString) return "";
  let result = templateString;
  for (const key in values) {
    // Ensure the key is directly on the object and not from prototype
    if (Object.prototype.hasOwnProperty.call(values, key)) {
      const regex = new RegExp(`\\{\\{\\s*${key}\\s*\\}\\}`, 'g');
      result = result.replace(regex, values[key]);
    }
  }
  return result;
};
</file>

<file path="utils/uuid.ts">
// Simple UUID v4 generator
export const generateUUID = (): string => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    const r = (Math.random() * 16) | 0;
    const v = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
};
</file>

<file path=".gitignore">
# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Diagnostic reports (https://nodejs.org/api/report.html)
report.[0-9]*.[0-9]*.[0-9]*.[0-9]*.json

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage (https://gruntjs.com/creating-plugins#storing-temporary-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release

# Dependency directories
node_modules/
jspm_packages/

# TypeScript v1 declaration files
typings/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.development.local
.env.test.local
.env.production.local
.env.local

# parcel-bundler cache files
.cache

# Next.js build output
.next
out

# Nuxt.js build output
.nuxt
dist

# Gatsby files
.cache/
# Comment in the public line in if your project uses Gatsby and not Next.js
# https://nextjs.org/blog/next-9-1#public-directory-support
# public

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Vite build output
dist

# Vite-specific cache directory
.vite

# Vitest coverage directory
coverage/
.DS_Store
</file>

<file path="App.tsx">
import React, { useState, useMemo, useCallback, useEffect, useRef } from 'react';
import { Prompt, Folder, SortOption, HierarchicalFolder, ExportData, HierarchicalTag } from './types';
import { ALL_PROMPTS_FOLDER_ID, SORT_OPTIONS_MAP, DEFAULT_FOLDER_NAME, PROMPTS_PER_PAGE as IMPORTED_PROMPTS_PER_PAGE } from './constants';
import { generateUUID } from './utils/uuid';
import { buildFolderHierarchy, isFolderBranchEmpty as checkFolderBranchEmptyLogic, getAllFolderIdsInBranch } from './utils/folderUtils';
import { buildTagHierarchy, getAllTagIdsInBranch } from './utils/tagUtils';
import Header from './components/Header';
import Sidebar from './components/Sidebar';
import PromptTile from './components/PromptTile';
import Modal from './components/Modal';
import PromptForm from './components/PromptForm';
import TagIcon from './components/icons/TagIcon';
import VersionHistoryModal from './components/VersionHistoryModal';
import HelpModal from './components/HelpModal';
import PaginationControls from './components/PaginationControls';
import {
  getAllPromptsDB,
  addPromptDB,
  updatePromptDB,
  deletePromptsByOriginalIdDB,
  getPromptsByOriginalIdDB,
  getAllFoldersDB,
  addFolderDB,
  updateFolderDB,
  deleteFoldersDB,
  initDefaultFolderDB,
  getFolderByIdDB,
  importDataDB,
} from './utils/idb';
import useTheme from './hooks/useTheme';
import { extractVariables } from './utils/templateUtils';
import FillTemplateModal from './components/FillTemplateModal';
import ChevronDownIcon from './components/icons/ChevronDownIcon';
import AIGenerationModal from './components/AIGenerationModal'; 
import { generateTextWithOllama } from './utils/ollamaApi'; // Changed to Ollama API utility

const PROMPTS_PER_PAGE = IMPORTED_PROMPTS_PER_PAGE || 12;

const App = () => {
  const [_theme, _toggleTheme] = useTheme();
  const [allPrompts, setAllPrompts] = useState<Prompt[]>([]);
  const [folders, setFolders] = useState<Folder[]>([]);
  const [defaultFolderId, setDefaultFolderId] = useState<string | null>(null);
  
  const [selectedFolderId, setSelectedFolderId] = useState<string | null>(ALL_PROMPTS_FOLDER_ID);
  const [currentSortOption, setCurrentSortOption] = useState<SortOption>(SortOption.RECENTLY_USED);
  
  const [isPromptModalOpen, setIsPromptModalOpen] = useState(false);
  const [promptToEdit, setPromptToEdit] = useState<Prompt | null>(null);
  
  const [isHistoryModalOpen, setIsHistoryModalOpen] = useState(false);
  const [historyPromptVersions, setHistoryPromptVersions] = useState<Prompt[]>([]);
  const [historyPromptTitle, setHistoryPromptTitle] = useState<string>('');

  const [isHelpModalOpen, setIsHelpModalOpen] = useState(false);

  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTag, setSelectedTag] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isProcessingData, setIsProcessingData] = useState(false); // For import/export

  // State for FillTemplateModal
  const [isFillTemplateModalOpen, setIsFillTemplateModalOpen] = useState(false);
  const [templateToFill, setTemplateToFill] = useState<Prompt | null>(null);

  // State for AI Generation Flow
  const [isAIGenerationModalOpen, setIsAIGenerationModalOpen] = useState(false);
  const [promptForAIGeneration, setPromptForAIGeneration] = useState<Prompt | null>(null);
  const [inputTextForAIGeneration, setInputTextForAIGeneration] = useState<string>('');
  const [aiGeneratedText, setAiGeneratedText] = useState<string | null>(null);
  const [isAIGenerating, setIsAIGenerating] = useState<boolean>(false);
  const [aiGenerationError, setAiGenerationError] = useState<string | null>(null);
  const [originatingAIFlow, setOriginatingAIFlow] = useState<boolean>(false);


  const importFileRef = useRef<HTMLInputElement>(null);
  const [currentPage, setCurrentPage] = useState(1);


  const loadData = useCallback(async (context: string = "initial") => {
    console.log(`[loadData called from ${context}] Starting data load...`);
    setIsLoading(true);
    try {
      // Fetch all data from DB first
      let [dbPrompts, dbFolders, dbDefaultFolder] = await Promise.all([
        getAllPromptsDB(),
        getAllFoldersDB(),
        initDefaultFolderDB()
      ]);

      // Prepare final state arrays
      let foldersToSet = [...dbFolders];
      let promptsToSet = [...dbPrompts];

      // Ensure default folder is included in the state
      if (!foldersToSet.find(f => f.id === dbDefaultFolder.id)) {
         foldersToSet.push(dbDefaultFolder);
      }
      
      // Handle migration for prompts that don't have a folderId
      const promptsToMigrate = promptsToSet.filter(p => p.folderId === null);
      if (promptsToMigrate.length > 0 && dbDefaultFolder.id) {
        
        const migratedPromptsMap = new Map<string, Prompt>();
        await Promise.all(promptsToMigrate.map(async p => {
          const updatedPrompt = { ...p, folderId: dbDefaultFolder.id };
          await updatePromptDB(updatedPrompt);
          migratedPromptsMap.set(updatedPrompt.id, updatedPrompt);
        }));

        // Update the local prompts array with the migrated versions
        promptsToSet = promptsToSet.map(p => migratedPromptsMap.get(p.id) || p);
      }
      
      // Set state once with final, corrected data
      setAllPrompts(promptsToSet);
      setFolders(foldersToSet);
      setDefaultFolderId(dbDefaultFolder.id);

    } catch (error) {
      console.error(`[loadData from ${context}] Error loading data from IndexedDB:`, error);
      alert("Error loading data. Please check the console for details.");
    } finally {
      setIsLoading(false);
    }
  }, []); // Empty dependency array is correct as it has no external dependencies.

  useEffect(() => {
    loadData("initial useEffect");
  }, [loadData]);


  const latestPrompts = useMemo(() => {
    const groupedByOriginalId = allPrompts.reduce((acc, prompt) => {
      acc[prompt.originalPromptId] = acc[prompt.originalPromptId] || [];
      acc[prompt.originalPromptId].push(prompt);
      return acc;
    }, {} as Record<string, Prompt[]>);

    return Object.values(groupedByOriginalId).map(versions => 
      versions.sort((a, b) => b.version - a.version)[0]
    );
  }, [allPrompts]);
  
  const promptVersionsCount = useMemo(() => {
    return allPrompts.reduce((acc, prompt) => {
      acc[prompt.originalPromptId] = (acc[prompt.originalPromptId] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
  }, [allPrompts]);


  const hierarchicalFolders = useMemo(() => buildFolderHierarchy(folders.filter(f => f.id !== defaultFolderId)), [folders, defaultFolderId]);
  const hierarchicalTags = useMemo(() => buildTagHierarchy(latestPrompts), [latestPrompts]);

  const handleAddPromptClick = () => {
    setPromptToEdit(null);
    setIsPromptModalOpen(true);
  };

  const handleEditPrompt = (prompt: Prompt) => {
    setPromptToEdit(prompt);
    setIsPromptModalOpen(true);
  };

  const handleDeletePrompt = async (originalPromptId: string) => {
    const promptToDelete = latestPrompts.find(p => p.originalPromptId === originalPromptId);
    if (window.confirm(`Are you sure you want to delete the prompt "${promptToDelete?.title || 'this prompt'}" and all its versions?`)) {
      await deletePromptsByOriginalIdDB(originalPromptId);
      setAllPrompts(prev => prev.filter(p => p.originalPromptId !== originalPromptId));
    }
  };

  const handleSavePrompt = async (
    promptData: Omit<Prompt, 'id' | 'originalPromptId' | 'version' | 'createdAt' | 'lastUsedAt' | 'timesUsed'>,
    existingPromptVersion?: Prompt | null
  ) => {
    let newPromptVersion: Prompt;
    if (existingPromptVersion) {
      newPromptVersion = {
        ...promptData,
        id: generateUUID(),
        originalPromptId: existingPromptVersion.originalPromptId,
        version: existingPromptVersion.version + 1,
        createdAt: new Date().toISOString(),
        lastUsedAt: null, 
        timesUsed: 0,    
      };
    } else {
      const newId = generateUUID();
      newPromptVersion = {
        ...promptData,
        id: newId,
        originalPromptId: newId,
        version: 1,
        createdAt: new Date().toISOString(),
        lastUsedAt: null,
        timesUsed: 0,
      };
    }
    await addPromptDB(newPromptVersion);
    setAllPrompts(prev => [...prev, newPromptVersion]);
    setIsPromptModalOpen(false);
    setPromptToEdit(null);
  };

  const updatePromptStats = useCallback(async (promptToUpdateStatsFor: Prompt) => {
    const latestVersionOfPrompt = allPrompts
      .filter(p => p.originalPromptId === promptToUpdateStatsFor.originalPromptId)
      .sort((a,b) => b.version - a.version)[0] || promptToUpdateStatsFor;

    const updatedPrompt = { 
      ...latestVersionOfPrompt, 
      timesUsed: latestVersionOfPrompt.timesUsed + 1, 
      lastUsedAt: new Date().toISOString() 
    };
    await updatePromptDB(updatedPrompt);
    setAllPrompts(prevAllPrompts => 
      prevAllPrompts.map(p => (p.id === updatedPrompt.id ? updatedPrompt : p))
    );
  }, [allPrompts]);


  // Handles "Use Template" button click OR direct copy from PromptTile
  const handleCopyOrUseTemplate = useCallback((prompt: Prompt) => {
    const variables = extractVariables(prompt.promptText);
    if (variables.length > 0) {
      setTemplateToFill(prompt);
      setOriginatingAIFlow(false); // This is for normal template usage, not AI flow
      setIsFillTemplateModalOpen(true);
    } else {
      // Direct copy is handled in PromptTile, here we just update stats
      updatePromptStats(prompt);
    }
  }, [updatePromptStats]);
  
  // Callback from FillTemplateModal
  const handleTemplateCompleted = useCallback(async (originalTemplatePrompt: Prompt, filledText: string) => {
    setIsFillTemplateModalOpen(false); // Close template fill modal first
    await updatePromptStats(originalTemplatePrompt); // Update stats for the original template
    
    if (originatingAIFlow) {
      setOriginatingAIFlow(false); // Reset the flag
      setPromptForAIGeneration(originalTemplatePrompt); // The original prompt whose template was filled
      setInputTextForAIGeneration(filledText); // The text with variables filled
      setAiGeneratedText(null); // Clear previous AI text
      setAiGenerationError(null); // Clear previous error
      setIsAIGenerationModalOpen(true); // Open AI generation modal
    } else {
      // This was a normal "Use Template" flow, text is already copied by FillTemplateModal
      // No further action needed here beyond stats update and closing modal
      setTemplateToFill(null);
    }
  }, [originatingAIFlow, updatePromptStats]);


  const handleViewHistory = async (originalPromptId: string) => {
    const versions = await getPromptsByOriginalIdDB(originalPromptId);
    const latestVersion = versions.sort((a,b) => b.version - a.version)[0];
    setHistoryPromptVersions(versions);
    setHistoryPromptTitle(latestVersion?.title || "Prompt");
    setIsHistoryModalOpen(true);
  };

  const handleAddFolder = async (folderName: string, parentId: string | null) => {
    const newFolder: Folder = { 
      id: generateUUID(), 
      name: folderName, 
      parentId,
      isDeletable: true,
      isRenamable: true,
    };
    await addFolderDB(newFolder);
    setFolders(prev => [...prev, newFolder]);
  };

  const handleRenameFolder = async (folderId: string, newName: string) => {
    const folderToRename = folders.find(f => f.id === folderId);
    if (folderToRename && folderToRename.isRenamable) {
        const siblings = folders.filter(s => s.parentId === folderToRename.parentId && s.id !== folderId);
        if (siblings.some(s => s.name.toLowerCase() === newName.toLowerCase())) {
          alert(`A folder named "${newName}" already exists in this location.`);
          return;
        }
      const updatedFolder = { ...folderToRename, name: newName };
      await updateFolderDB(updatedFolder);
      setFolders(prev => prev.map(f => (f.id === folderId ? updatedFolder : f)));
    }
  };

  const handleDeleteFolder = async (folderId: string) => {
    const folderToDelete = folders.find(f => f.id === folderId);
    if (!folderToDelete || !folderToDelete.isDeletable) {
      alert("This folder cannot be deleted.");
      return;
    }
    
    // This is the main safety check. It ensures no prompts (any version) exist in the entire branch.
    if (!checkFolderBranchEmptyLogic(folderId, allPrompts, folders)) {
      alert("Cannot delete. The folder or its subfolders contain prompts. Please move or delete the prompts first.");
      return;
    }

    const folderIdsToDelete = getAllFolderIdsInBranch(folderId, folders);
    const hasSubfolders = folderIdsToDelete.length > 1;

    const confirmationMessage = `Are you sure you want to delete the folder "${folderToDelete.name}"?` + 
      (hasSubfolders ? " All of its empty subfolders will also be deleted." : "") +
      " This action cannot be undone.";

    if (window.confirm(confirmationMessage)) {
      await deleteFoldersDB(folderIdsToDelete);
      
      setFolders(prev => prev.filter(f => !folderIdsToDelete.includes(f.id)));

      // If the currently selected folder was one of the deleted ones, reset selection.
      if (selectedFolderId && folderIdsToDelete.includes(selectedFolderId)) {
        setSelectedFolderId(ALL_PROMPTS_FOLDER_ID);
      }
    }
  };
  
  const isFolderBranchEmptyCallback = useCallback((folderId: string) => {
    // Check against all prompts for data integrity, not just the latest visible ones.
    return checkFolderBranchEmptyLogic(folderId, allPrompts, folders);
  }, [allPrompts, folders]);

  const allTags = useMemo(() => {
    const tagsSet = new Set<string>();
    latestPrompts.forEach(prompt => prompt.tags.forEach(tag => tagsSet.add(tag)));
    return Array.from(tagsSet).sort();
  }, [latestPrompts]);

  const filteredAndSortedPrompts = useMemo(() => {
    let filtered = latestPrompts;

    if (selectedTag) {
        const selectedTagNode = hierarchicalTags.flatMap(t => getAllTagIdsInBranch(t)).find(t => t === selectedTag);
        if (selectedTagNode) {
            const branchTags = getAllTagIdsInBranch(selectedTagNode);
            filtered = filtered.filter(p => p.tags.some(t => branchTags.includes(t)));
        }
    } else if (selectedFolderId === ALL_PROMPTS_FOLDER_ID) { /* Show all */ }
    else if (selectedFolderId === null && defaultFolderId) { 
      filtered = filtered.filter(p => p.folderId === defaultFolderId);
    } else if (selectedFolderId) {
      const branchFolderIds = getAllFolderIdsInBranch(selectedFolderId, folders);
      filtered = filtered.filter(p => p.folderId !== null && branchFolderIds.includes(p.folderId));
    } else if (selectedFolderId === null && !defaultFolderId) {
        // This case should ideally not be reached if defaultFolderId is always available
        // but as a fallback, show prompts with no folderId explicitly set.
        filtered = filtered.filter(p => p.folderId === null); 
    }

    if (searchTerm.trim() !== '') {
      const lowerSearchTerm = searchTerm.toLowerCase();
      filtered = filtered.filter(p =>
        p.title.toLowerCase().includes(lowerSearchTerm) ||
        p.description.toLowerCase().includes(lowerSearchTerm) ||
        p.promptText.toLowerCase().includes(lowerSearchTerm)
      );
    }
    
    return [...filtered].sort((a, b) => {
      switch (currentSortOption) {
        case SortOption.NAME_ASC: return a.title.localeCompare(b.title);
        case SortOption.NAME_DESC: return b.title.localeCompare(a.title);
        case SortOption.MOST_USED: return b.timesUsed - a.timesUsed;
        case SortOption.RECENTLY_USED:
          if (!a.lastUsedAt && !b.lastUsedAt) return 0;
          if (!a.lastUsedAt) return 1; if (!b.lastUsedAt) return -1;
          return new Date(b.lastUsedAt!).getTime() - new Date(a.lastUsedAt!).getTime();
        case SortOption.DATE_CREATED_ASC: return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
        case SortOption.DATE_CREATED_DESC: return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        default: // Default to recently used
          if (!a.lastUsedAt && !b.lastUsedAt) return 0;
          if (!a.lastUsedAt) return 1; if (!b.lastUsedAt) return -1;
          return new Date(b.lastUsedAt!).getTime() - new Date(a.lastUsedAt!).getTime();
      }
    });
  }, [latestPrompts, selectedFolderId, defaultFolderId, folders, searchTerm, selectedTag, currentSortOption, hierarchicalTags]);

  useEffect(() => { setCurrentPage(1); }, [selectedFolderId, searchTerm, selectedTag, currentSortOption]);

  const totalPages = useMemo(() => Math.ceil(filteredAndSortedPrompts.length / PROMPTS_PER_PAGE), [filteredAndSortedPrompts.length]);
  const paginatedPrompts = useMemo(() => {
    const startIndex = (currentPage - 1) * PROMPTS_PER_PAGE;
    return filteredAndSortedPrompts.slice(startIndex, startIndex + PROMPTS_PER_PAGE);
  }, [filteredAndSortedPrompts, currentPage]);


  // --- AI Generation Handlers ---
  const handleTriggerAIGeneration = useCallback((prompt: Prompt) => {
    const variables = extractVariables(prompt.promptText);
    setAiGeneratedText(null); // Clear previous results
    setAiGenerationError(null); // Clear previous error
    
    if (variables.length > 0) {
      setTemplateToFill(prompt);
      setOriginatingAIFlow(true); // Signal that FillTemplateModal is part of AI flow
      setIsFillTemplateModalOpen(true);
    } else {
      // No template variables, proceed directly to AI generation modal
      setPromptForAIGeneration(prompt);
      setInputTextForAIGeneration(prompt.promptText);
      setIsAIGenerationModalOpen(true);
    }
  }, []);

  const handleExecuteAIGeneration = useCallback(async (textToGenerateFrom: string) => {
    if (!promptForAIGeneration) {
        setAiGenerationError("Cannot generate AI text: No base prompt selected.");
        return; 
    }

    setIsAIGenerating(true);
    setAiGeneratedText(null);
    setAiGenerationError(null);
    try {
      // Use the new Ollama utility function
      const generatedText = await generateTextWithOllama(textToGenerateFrom);
      setAiGeneratedText(generatedText);
      // Optionally update usage stats for the prompt used for generation
      await updatePromptStats(promptForAIGeneration);
    } catch (error: any) {
      console.error("AI Generation failed (Ollama):", error);
      // The ollamaApi.ts should have already alerted the user for specific errors.
      // Here, we set the error message for display in the AIGenerationModal.
      setAiGenerationError(error.message || "An unknown error occurred during AI generation with Ollama.");
    } finally {
      setIsAIGenerating(false);
    }
  }, [promptForAIGeneration, updatePromptStats]);

  const handleCloseAIGenerationModal = useCallback(() => {
    setIsAIGenerationModalOpen(false);
    setPromptForAIGeneration(null);
    setInputTextForAIGeneration('');
    setAiGeneratedText(null);
    setIsAIGenerating(false);
    setAiGenerationError(null);
  }, []);

  // --- Import/Export Handlers ---
  const handleExportData = async () => {
    setIsProcessingData(true);
    try {
      const promptsToExport = await getAllPromptsDB();
      const foldersToExport = await getAllFoldersDB();
      const exportData: ExportData = {
        prompts: promptsToExport,
        folders: foldersToExport,
      };
      const jsonString = JSON.stringify(exportData, null, 2);
      const blob = new Blob([jsonString], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      const date = new Date().toISOString().slice(0, 10);
      a.download = `ai-prompt-manager-backup-${date}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      alert('Data exported successfully!');
    } catch (error) {
      console.error('Error exporting data:', error);
      alert('Failed to export data. Check console for details.');
    } finally {
      setIsProcessingData(false);
    }
  };

  const handleImportDataTrigger = () => {
    if (importFileRef.current) {
      importFileRef.current.click();
    }
  };

  const handleImportFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;
    const confirmation = window.confirm(
      "WARNING: Importing data will replace ALL current prompts and folders. This action CANNOT BE UNDONE. Are you sure?"
    );
    if (!confirmation) {
       if (importFileRef.current) importFileRef.current.value = ''; 
      return;
    }
    setIsProcessingData(true);
    const reader = new FileReader();
    reader.onload = async (e) => {
      try {
        const jsonString = e.target?.result as string;
        const importedData = JSON.parse(jsonString) as ExportData;
        if (!importedData || !Array.isArray(importedData.prompts) || !Array.isArray(importedData.folders)) {
          throw new Error('Invalid file format.');
        }

        // Use the new transactional import function
        await importDataDB(importedData);

        alert('Data imported successfully! The application will now reload with the new data.');
        await loadData("after import"); 
      } catch (error: any) {
        alert(`Failed to import data: ${error.message}`);
        console.error('Import error:', error);
      } finally {
        setIsProcessingData(false);
        if (importFileRef.current) importFileRef.current.value = ''; 
      }
    };
    reader.onerror = () => {
      alert('Failed to read the import file.');
      setIsProcessingData(false);
      if (importFileRef.current) importFileRef.current.value = '';
    };
    reader.readAsText(file);
  };


  if (isLoading || isProcessingData) {
    return (
      <div className="flex items-center justify-center h-screen bg-neutral-100 dark:bg-neutral-900">
        <div className="text-2xl font-semibold text-neutral-700 dark:text-neutral-200">
          {isProcessingData ? 'Processing Data...' : 'Loading Prompts...'}
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen flex-col bg-neutral-100 dark:bg-neutral-900">
      <Header 
        onAddPrompt={handleAddPromptClick} 
        onToggleHelp={() => setIsHelpModalOpen(true)}
        onExportData={handleExportData}
        onImportDataTrigger={handleImportDataTrigger}
      />
      <input type="file" accept=".json" ref={importFileRef} onChange={handleImportFileSelect} style={{ display: 'none' }} />
      
      <div className="flex flex-1 overflow-hidden">
        <Sidebar
          hierarchicalFolders={hierarchicalFolders}
          allFolders={folders}
          selectedFolderId={selectedFolderId}
          onSelectFolder={setSelectedFolderId}
          onAddFolder={handleAddFolder}
          onRenameFolder={handleRenameFolder}
          onDeleteFolder={handleDeleteFolder}
          isFolderBranchEmpty={isFolderBranchEmptyCallback}
          hierarchicalTags={hierarchicalTags}
          selectedTag={selectedTag}
          onSelectTag={setSelectedTag}
        />
        <main className="flex-1 p-6 overflow-y-auto">
          <div className="mb-6 flex flex-col sm:flex-row justify-between items-center gap-4">
            {/* Search and Filter Controls */}
            <div className="relative w-full sm:max-w-xs">
              <input type="search" placeholder="Search prompts..." value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-light focus:border-transparent bg-white dark:bg-neutral-700 text-neutral-900 dark:text-neutral-100" />
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg className="w-5 h-5 text-neutral-400 dark:text-neutral-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" /></svg>
              </div>
            </div>
            <div className="flex flex-col sm:flex-row gap-4 w-full sm:w-auto">
              {allTags.length > 0 && (
                <div className="relative w-full sm:w-auto">
                  <select value={selectedTag || ''} onChange={(e) => setSelectedTag(e.target.value || null)}
                    className="w-full appearance-none pl-3 pr-10 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-light focus:border-transparent bg-white dark:bg-neutral-700 text-neutral-900 dark:text-neutral-100">
                    <option value="">All Tags</option>
                    {allTags.map(tag => (<option key={tag} value={tag}>{tag}</option>))}
                  </select>
                  <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none"><TagIcon className="w-4 h-4 text-neutral-400 dark:text-neutral-500" /></div>
                </div>
              )}
              <div className="relative w-full sm:w-auto">
                <select value={currentSortOption} onChange={(e) => setCurrentSortOption(e.target.value as SortOption)}
                  className="w-full appearance-none pl-3 pr-10 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-primary dark:focus:ring-primary-light focus:border-transparent bg-white dark:bg-neutral-700 text-neutral-900 dark:text-neutral-100">
                  {Object.entries(SORT_OPTIONS_MAP).map(([key, value]) => (<option key={key} value={key}>{value}</option>))}
                </select>
                 <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none"><ChevronDownIcon className="w-4 h-4 text-neutral-400 dark:text-neutral-500" /></div>
              </div>
            </div>
          </div>

          {paginatedPrompts.length > 0 ? (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {paginatedPrompts.map(prompt => (
                  <PromptTile
                    key={prompt.id}
                    prompt={prompt}
                    onCopy={handleCopyOrUseTemplate}
                    onEdit={handleEditPrompt}
                    onDelete={handleDeletePrompt}
                    onViewHistory={handleViewHistory}
                    onGenerateWithAI={handleTriggerAIGeneration} 
                    hasMultipleVersions={(promptVersionsCount[prompt.originalPromptId] || 1) > 1}
                  />
                ))}
              </div>
              {totalPages > 1 && (<PaginationControls currentPage={currentPage} totalPages={totalPages} onPageChange={setCurrentPage} />)}
            </>
          ) : ( 
            <div className="text-center py-12">
              <svg className="mx-auto h-12 w-12 text-neutral-400 dark:text-neutral-500" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true"><path vectorEffect="non-scaling-stroke" strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 13h6m-3-3v6m-9 1V7a2 2 0 012-2h6l2 2h6a2 2 0 012 2v8a2 2 0 01-2 2H5a2 2 0 01-2-2z" /></svg>
              <h3 className="mt-2 text-lg font-medium text-neutral-900 dark:text-neutral-100">No prompts found</h3>
              <p className="mt-1 text-sm text-neutral-500 dark:text-neutral-400">
                {searchTerm || selectedTag || (selectedFolderId && selectedFolderId !== ALL_PROMPTS_FOLDER_ID) ? "Try adjusting your search or filters, or " : "Get started by "}
                <button onClick={handleAddPromptClick} className="font-medium text-primary hover:text-primary-dark dark:text-primary-light dark:hover:text-primary">adding a new prompt</button>.
              </p>
            </div>
          )}
        </main>
      </div>

      {isPromptModalOpen && defaultFolderId && (
        <Modal isOpen={isPromptModalOpen} onClose={() => setIsPromptModalOpen(false)} title={promptToEdit ? 'Edit Prompt (New Version)' : 'Add New Prompt'}>
          <PromptForm promptToEdit={promptToEdit} folders={folders} onSave={handleSavePrompt} onClose={() => { setIsPromptModalOpen(false); setPromptToEdit(null); }} defaultFolderId={defaultFolderId} />
        </Modal>
      )}

      {isHistoryModalOpen && (<VersionHistoryModal isOpen={isHistoryModalOpen} onClose={() => setIsHistoryModalOpen(false)} promptVersions={historyPromptVersions} originalPromptTitle={historyPromptTitle} />)}
      {isHelpModalOpen && (<HelpModal isOpen={isHelpModalOpen} onClose={() => setIsHelpModalOpen(false)} />)}

      {isFillTemplateModalOpen && templateToFill && (
        <FillTemplateModal
          isOpen={isFillTemplateModalOpen}
          onClose={() => { setIsFillTemplateModalOpen(false); setTemplateToFill(null); setOriginatingAIFlow(false); }}
          templatePrompt={templateToFill}
          onGenerateAndCopy={handleTemplateCompleted} 
        />
      )}

      {isAIGenerationModalOpen && promptForAIGeneration && (
        <AIGenerationModal
          isOpen={isAIGenerationModalOpen}
          onClose={handleCloseAIGenerationModal}
          promptTitle={promptForAIGeneration.title}
          inputText={inputTextForAIGeneration}
          generatedText={aiGeneratedText}
          isGenerating={isAIGenerating}
          onGenerate={handleExecuteAIGeneration}
          errorMessage={aiGenerationError}
        />
      )}
    </div>
  );
};

export default App;
</file>

<file path="constants.ts">
import { SortOption } from './types';

export const ALL_PROMPTS_FOLDER_ID = 'all_prompts_folder_id';

export const SORT_OPTIONS_MAP: Record<SortOption, string> = {
  [SortOption.NAME_ASC]: 'Name (A-Z)',
  [SortOption.NAME_DESC]: 'Name (Z-A)',
  [SortOption.MOST_USED]: 'Most Used',
  [SortOption.RECENTLY_USED]: 'Recently Used',
  [SortOption.DATE_CREATED_ASC]: 'Date Created (Oldest)',
  [SortOption.DATE_CREATED_DESC]: 'Date Created (Newest)',
};

export const DEFAULT_FOLDER_NAME = "Uncategorized";

export const PROMPTS_PER_PAGE = 12;
</file>

<file path="dev_runner.txt">
#!/bin/bash

# --- Configuration ---
# Command to start your development server
# Example: "npm run dev", "yarn dev", "python -m http.server 8000", etc.
SERVER_COMMAND="npm run dev"

# URL of your application
APP_URL="http://localhost:5173" # Common for Vite, adjust if needed

# File to store the server's PID
PID_FILE=".server.pid"

# Log file for server output
LOG_FILE="server_dev.log"

# Seconds to wait for the server to start before launching browser
SERVER_WAIT_SECONDS=5

# --- Functions ---
cleanup() {
  echo "" # Newline after user presses Enter or Ctrl+C
  echo "--- Cleaning up ---"
  if [ -f "$PID_FILE" ]; then
    SERVER_PID=$(cat "$PID_FILE")
    if ps -p "$SERVER_PID" > /dev/null; then
      echo "Attempting to stop server (PID: $SERVER_PID)..."
      # Try to terminate gracefully first
      kill "$SERVER_PID"
      # Wait a bit for graceful shutdown
      sleep 2
      # If still running, force kill
      if ps -p "$SERVER_PID" > /dev/null; then
        echo "Server did not stop gracefully, forcing shutdown (SIGKILL)..."
        kill -9 "$SERVER_PID"
      else
        echo "Server stopped."
      fi
    else
      echo "Server (PID: $SERVER_PID) was not running or PID file is stale."
    fi
    rm -f "$PID_FILE"
    echo "Removed PID file."
  else
    echo "No PID file found. Server might not have started correctly or was already stopped."
  fi
  echo "Cleanup complete. Exiting script."
  exit 0
}

# Trap SIGINT (Ctrl+C) and SIGTERM (kill command) to run cleanup
trap cleanup SIGINT SIGTERM EXIT

# --- Main Script ---
echo "--- Starting Development Environment ---"

# Remove old PID file if it exists
if [ -f "$PID_FILE" ]; then
  rm -f "$PID_FILE"
  echo "Removed old PID file."
fi

echo "Starting server with command: $SERVER_COMMAND"
echo "Server output will be logged to: $LOG_FILE"

# Start the server in the background and save its PID
# Redirect stdout and stderr to the log file
nohup $SERVER_COMMAND > "$LOG_FILE" 2>&1 &
SERVER_PID=$!
echo "$SERVER_PID" > "$PID_FILE"

echo "Server started in background (PID: $SERVER_PID)."
echo "Waiting $SERVER_WAIT_SECONDS seconds for server to initialize..."
sleep "$SERVER_WAIT_SECONDS"

# Check if server is still running
if ps -p "$SERVER_PID" > /dev/null; then
  echo "Server process (PID: $SERVER_PID) is running."
  echo "Opening browser at $APP_URL..."
  # Use 'open' for macOS, 'xdg-open' for Linux, or 'start' for Windows/Git Bash
  if [[ "$OSTYPE" == "darwin"* ]]; then
    open "$APP_URL"
  elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    xdg-open "$APP_URL"
  elif [[ "$OSTYPE" == "cygwin" ]] || [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "win32" ]]; then
    start "$APP_URL"
  else
    echo "Unsupported OS for automatic browser opening. Please open $APP_URL manually."
  fi
  echo ""
  echo "---------------------------------------------------------------------"
  echo "  Development server is running. Browser opened."
  echo "  View server logs in: $LOG_FILE"
  echo "  Press [Enter] in this terminal when you are done to stop the server."
  echo "---------------------------------------------------------------------"
  # Wait for user to press Enter
  read -r
else
  echo ""
  echo "!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!"
  echo "  ERROR: Server process (PID: $SERVER_PID) does not seem to be running."
  echo "  Please check the log file for errors: $LOG_FILE"
  echo "!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!"
  # Cleanup will still run due to the trap
fi

# The 'trap' command will handle cleanup when the script exits from here
# (either by the user pressing Enter, or by other means like Ctrl+C if 'read' is interrupted)
</file>

<file path="dummy-prompts.json">
{
  "prompts": [
    {
      "id": "d1a7a8f7-c2d0-4f7a-8a4e-1b2a3c4d5e01",
      "originalPromptId": "opid-001",
      "version": 1,
      "title": "Blog Post Idea Generator",
      "description": "Generates 5 blog post ideas for a given keyword.",
      "promptText": "Generate 5 compelling blog post titles and brief outlines about {{topic}}. The tone should be {{tone_style}}.",
      "tags": ["blogging", "content creation", "marketing"],
      "folderId": "folder-mktg-01",
      "createdAt": "2023-10-01T10:00:00.000Z",
      "lastUsedAt": "2023-10-26T14:30:00.000Z",
      "timesUsed": 15
    },
    {
      "id": "e2b8b9f8-d3e1-4g8b-9b5f-2c3b4d5e6f02",
      "originalPromptId": "opid-001",
      "version": 2,
      "title": "Advanced Blog Post Idea Generator",
      "description": "Generates 5 detailed blog post ideas with target audience for a given keyword.",
      "promptText": "For the keyword '{{topic}}', generate 5 compelling blog post titles. For each title, provide a brief 2-sentence outline and suggest a primary target audience. The tone should be {{tone_style}} and aimed at {{audience_level}}.",
      "tags": ["blogging", "seo", "marketing", "content strategy"],
      "folderId": "folder-mktg-01",
      "createdAt": "2023-10-15T11:00:00.000Z",
      "lastUsedAt": "2023-11-05T10:00:00.000Z",
      "timesUsed": 25
    },
    {
      "id": "f3c9ca09-e4f2-4h9c-ac60-3d4c5e6f7003",
      "originalPromptId": "opid-002",
      "version": 1,
      "title": "Python Function Explainer",
      "description": "Explains a Python function in simple terms.",
      "promptText": "Explain the following Python code snippet like I'm a beginner: \n```python\n{{python_code}}\n```\nFocus on what it does and why it's useful.",
      "tags": ["python", "coding", "explanation"],
      "folderId": "folder-code-02",
      "createdAt": "2023-10-02T12:00:00.000Z",
      "lastUsedAt": "2023-11-01T16:00:00.000Z",
      "timesUsed": 8
    },
    {
      "id": "g4d0db10-f503-4i0d-bd71-4e5d6f708114",
      "originalPromptId": "opid-003",
      "version": 1,
      "title": "Short Story Starter",
      "description": "Provides a starting paragraph for a short story based on a genre.",
      "promptText": "Write an intriguing opening paragraph (around 100 words) for a {{genre}} short story. The story should hint at a central mystery.",
      "tags": ["creative writing", "fiction", "story idea"],
      "folderId": "folder-creative-03",
      "createdAt": "2023-09-05T14:00:00.000Z",
      "lastUsedAt": "2023-10-20T11:00:00.000Z",
      "timesUsed": 12
    },
    {
      "id": "h5e1ec21-0614-4j1e-ce82-5f6e70819225",
      "originalPromptId": "opid-003",
      "version": 2,
      "title": "Advanced Short Story Starter",
      "description": "Provides a starting paragraph and a character name for a short story.",
      "promptText": "Write an intriguing opening paragraph (around 100-150 words) for a {{genre}} short story featuring a character named {{character_name}}. The story should hint at a central mystery and a unique setting.",
      "tags": ["creative writing", "fiction", "story idea", "character development"],
      "folderId": "folder-creative-03",
      "createdAt": "2023-10-25T14:00:00.000Z",
      "lastUsedAt": "2023-11-03T18:00:00.000Z",
      "timesUsed": 5
    },
    {
      "id": "i6f2fd32-1725-4k2f-df93-607f81920336",
      "originalPromptId": "opid-004",
      "version": 1,
      "title": "Email Subject Line Creator",
      "description": "Creates 3 catchy email subject lines.",
      "promptText": "Generate 3 catchy and click-worthy email subject lines for an email about {{email_topic}}.",
      "tags": ["email marketing", "copywriting"],
      "folderId": "folder-mktg-01",
      "createdAt": "2023-11-01T09:00:00.000Z",
      "lastUsedAt": "2023-11-06T09:15:00.000Z",
      "timesUsed": 22
    },
    {
      "id": "j7030e43-2836-4l30-e0a4-718092031447",
      "originalPromptId": "opid-005",
      "version": 1,
      "title": "JavaScript Array Method Finder",
      "description": "Suggests a JS array method for a specific task.",
      "promptText": "I want to {{array_task}} in JavaScript. What's the best modern array method to use and can you provide a simple example?",
      "tags": ["javascript", "coding", "arrays"],
      "folderId": "folder-code-02",
      "createdAt": "2023-10-28T17:00:00.000Z",
      "lastUsedAt": "2023-11-04T12:30:00.000Z",
      "timesUsed": 18
    },
    {
      "id": "k8141f54-3947-4m41-f1b5-829103142558",
      "originalPromptId": "opid-006",
      "version": 1,
      "title": "Poem Theme Generator",
      "description": "Generates a theme for a short poem.",
      "promptText": "Suggest a unique and thought-provoking theme for a short poem (around 4-8 lines).",
      "tags": ["creative writing", "poetry"],
      "folderId": "folder-creative-03",
      "createdAt": "2023-08-15T10:00:00.000Z",
      "lastUsedAt": "2023-10-10T10:00:00.000Z",
      "timesUsed": 30
    },
    {
      "id": "l9252065-4a58-4n52-02c6-930214253669",
      "originalPromptId": "opid-007",
      "version": 1,
      "title": "Product Description Enhancer",
      "description": "Rewrites a product description to be more persuasive.",
      "promptText": "Rewrite the following product description to be more persuasive and highlight its key benefits. Original description: {{product_description}}",
      "tags": ["copywriting", "ecommerce", "marketing"],
      "folderId": "folder-mktg-01",
      "createdAt": "2023-11-02T11:30:00.000Z",
      "lastUsedAt": "2023-11-06T11:35:00.000Z",
      "timesUsed": 11
    },
    {
      "id": "m0363176-5b69-4o63-13d7-a41325364770",
      "originalPromptId": "opid-007",
      "version": 2,
      "title": "Benefit-Driven Product Description",
      "description": "Creates a benefit-driven product description from features.",
      "promptText": "Take these product features: {{product_features}}. Write a persuasive, benefit-driven product description (around 100 words) for {{target_customer}}.",
      "tags": ["copywriting", "ecommerce", "marketing", "persuasion"],
      "folderId": "folder-mktg-01",
      "createdAt": "2023-11-05T14:30:00.000Z",
      "lastUsedAt": "2023-11-07T10:00:00.000Z",
      "timesUsed": 5
    },
    {
      "id": "n1474287-6c7a-4p74-24e8-b52436475881",
      "originalPromptId": "opid-008",
      "version": 1,
      "title": "SQL Query Optimizer",
      "description": "Suggests optimizations for a given SQL query.",
      "promptText": "How can I optimize the following SQL query for better performance?\n```sql\n{{sql_query}}\n```\nExplain the reasons for your suggestions.",
      "tags": ["sql", "database", "performance"],
      "folderId": "folder-code-02",
      "createdAt": "2023-10-12T13:15:00.000Z",
      "lastUsedAt": "2023-10-30T13:20:00.000Z",
      "timesUsed": 7
    },
    {
      "id": "o2585398-7d8b-4q85-35f9-c63547586992",
      "originalPromptId": "opid-009",
      "version": 1,
      "title": "Worldbuilding Detail - Culture",
      "description": "Generates a unique cultural detail for a fantasy world.",
      "promptText": "Describe a unique cultural festival or tradition for a fantasy world where {{world_premise}}.",
      "tags": ["creative writing", "worldbuilding", "fantasy"],
      "folderId": "folder-creative-03",
      "createdAt": "2023-09-20T16:00:00.000Z",
      "lastUsedAt": "2023-10-18T16:05:00.000Z",
      "timesUsed": 14
    },
    {
      "id": "p3696409-8e9c-4r96-460a-d74658697003",
      "originalPromptId": "opid-010",
      "version": 1,
      "title": "Social Media Post - Event",
      "description": "Drafts a short social media post for an upcoming event.",
      "promptText": "Draft a short, engaging social media post (for Twitter/X) announcing an upcoming event: {{event_name}} on {{event_date}}. Include a call to action: {{cta}}.",
      "tags": ["social media", "marketing", "event promotion"],
      "folderId": null,
      "createdAt": "2023-11-03T10:00:00.000Z",
      "lastUsedAt": "2023-11-07T11:00:00.000Z",
      "timesUsed": 19
    },
    {
      "id": "q4707510-9f0d-4s07-571b-e85769708114",
      "originalPromptId": "opid-011",
      "version": 1,
      "title": "Code Comment Generator",
      "description": "Generates a clear comment for a block of code.",
      "promptText": "Write a concise and clear comment explaining what the following code block does:\n```{{language}}\n{{code_block}}\n```",
      "tags": ["coding", "documentation"],
      "folderId": "folder-code-02",
      "createdAt": "2023-10-05T15:45:00.000Z",
      "lastUsedAt": "2023-10-29T15:50:00.000Z",
      "timesUsed": 9
    },
    {
      "id": "r5818621-a01e-4t18-682c-f96870819225",
      "originalPromptId": "opid-011",
      "version": 2,
      "title": "Function Docstring Generator (Python)",
      "description": "Generates a Python docstring for a function.",
      "promptText": "Write a PEP 257 compliant docstring for the following Python function:\n```python\n{{python_function_code}}\n```",
      "tags": ["python", "coding", "documentation", "docstring"],
      "folderId": "folder-code-02",
      "createdAt": "2023-10-20T10:00:00.000Z",
      "lastUsedAt": "2023-11-02T10:05:00.000Z",
      "timesUsed": 13
    },
    {
      "id": "s6929732-b12f-4u29-793d-0a7981920336",
      "originalPromptId": "opid-012",
      "version": 1,
      "title": "Character Dialogue Writer",
      "description": "Writes a short dialogue between two characters.",
      "promptText": "Write a short piece of dialogue (4-6 lines) between two characters, {{char1_name}} (who is {{char1_trait}}) and {{char2_name}} (who is {{char2_trait}}), about {{dialogue_topic}}.",
      "tags": ["creative writing", "dialogue", "fiction"],
      "folderId": "folder-creative-03",
      "createdAt": "2023-08-22T11:00:00.000Z",
      "lastUsedAt": "2023-10-22T11:05:00.000Z",
      "timesUsed": 20
    },
    {
      "id": "t7030843-c230-4v30-8a4e-1b8092031447",
      "originalPromptId": "opid-013",
      "version": 1,
      "title": "Ad Copy Variation",
      "description": "Generates two variations of an ad copy.",
      "promptText": "Generate two different versions of ad copy (max 30 words each) for a product called {{product_name}} that helps with {{product_benefit}}.",
      "tags": ["marketing", "advertising", "copywriting"],
      "folderId": "folder-mktg-01",
      "createdAt": "2023-11-04T14:00:00.000Z",
      "lastUsedAt": "2023-11-06T14:05:00.000Z",
      "timesUsed": 6
    },
    {
      "id": "u8141954-d341-4w41-9b5f-2c9103142558",
      "originalPromptId": "opid-014",
      "version": 1,
      "title": "API Endpoint Naming",
      "description": "Suggests RESTful API endpoint names for a resource.",
      "promptText": "Suggest RESTful API endpoint names (including HTTP methods) for managing a '{{resource_name}}' resource.",
      "tags": ["api", "coding", "rest"],
      "folderId": null,
      "createdAt": "2023-10-18T09:30:00.000Z",
      "lastUsedAt": "2023-11-01T09:35:00.000Z",
      "timesUsed": 10
    },
    {
      "id": "v9252065-e452-4x52-ac60-3d0214253669",
      "originalPromptId": "opid-015",
      "version": 1,
      "title": "Flash Fiction Prompt",
      "description": "A prompt for a very short story (flash fiction).",
      "promptText": "Write a complete flash fiction story (under 300 words) based on the following three words: {{word1}}, {{word2}}, {{word3}}.",
      "tags": ["creative writing", "flash fiction", "story prompt"],
      "folderId": "folder-creative-03",
      "createdAt": "2023-07-30T12:00:00.000Z",
      "lastUsedAt": "2023-10-28T12:05:00.000Z",
      "timesUsed": 28
    },
    {
      "id": "w0363176-f563-4y63-bd71-4e1325364770",
      "originalPromptId": "opid-015",
      "version": 2,
      "title": "Image-Inspired Flash Fiction",
      "description": "A flash fiction prompt inspired by an image description.",
      "promptText": "Write a complete flash fiction story (under 300 words) inspired by this image description: A {{image_description}}.",
      "tags": ["creative writing", "flash fiction", "visual prompt"],
      "folderId": "folder-creative-03",
      "createdAt": "2023-09-15T14:20:00.000Z",
      "lastUsedAt": "2023-11-05T14:25:00.000Z",
      "timesUsed": 3
    },
    {
      "id": "x1474287-0674-4z74-ce82-5f2436475881",
      "originalPromptId": "opid-016",
      "version": 1,
      "title": "Personalized Thank You Note",
      "description": "Drafts a personalized thank you note.",
      "promptText": "Draft a short, sincere thank you note to {{recipient_name}} for {{reason_for_thanks}}. Mention specifically {{specific_detail}}.",
      "tags": ["communication", "etiquette"],
      "folderId": null,
      "createdAt": "2023-11-05T10:10:00.000Z",
      "lastUsedAt": "2023-11-07T10:15:00.000Z",
      "timesUsed": 4
    },
    {
      "id": "y2585398-1785-4a85-df93-603547586992",
      "originalPromptId": "opid-017",
      "version": 1,
      "title": "Meeting Agenda Creator",
      "description": "Creates a basic meeting agenda.",
      "promptText": "Create a basic agenda for a {{meeting_duration_hours}} hour meeting about {{meeting_topic}}. Include time slots for introduction, {{discussion_point_1}}, {{discussion_point_2}}, and Q&A.",
      "tags": ["productivity", "meetings"],
      "folderId": null,
      "createdAt": "2023-10-26T17:00:00.000Z",
      "lastUsedAt": "2023-10-26T17:05:00.000Z",
      "timesUsed": 2
    },
    {
      "id": "z3696409-2896-4b96-e0a4-714658697003",
      "originalPromptId": "opid-018",
      "version": 1,
      "title": "Code Refactoring Ideas",
      "description": "Suggests ideas for refactoring a piece of code.",
      "promptText": "I have a piece of code that {{code_description}}. What are some general principles or specific ideas I could apply to refactor it for better {{refactor_goal_1}} and {{refactor_goal_2}}?",
      "tags": ["coding", "refactoring", "software design"],
      "folderId": "folder-code-02",
      "createdAt": "2023-11-06T15:00:00.000Z",
      "lastUsedAt": null,
      "timesUsed": 0
    },
    {
      "id": "a4707510-3907-4c07-f1b5-825769708114",
      "originalPromptId": "opid-018",
      "version": 2,
      "title": "Specific Code Refactoring",
      "description": "Suggests refactoring for a specific code snippet.",
      "promptText": "Given the following {{language}} code:\n```{{language}}\n{{code_snippet}}\n```\nSuggest specific ways to refactor it to improve {{refactor_goal}}. Explain your reasoning.",
      "tags": ["coding", "refactoring", "software design", "code review"],
      "folderId": "folder-code-02",
      "createdAt": "2023-11-07T09:00:00.000Z",
      "lastUsedAt": "2023-11-07T09:05:00.000Z",
      "timesUsed": 1
    },
    {
      "id": "b5818621-4a18-4d18-02c6-936870819225",
      "originalPromptId": "opid-019",
      "version": 1,
      "title": "Debate Point Summarizer",
      "description": "Summarizes key points for one side of a debate.",
      "promptText": "Summarize 3 key arguments for the '{{debate_side}}' position on the topic of '{{debate_topic}}'. Keep each argument concise (1-2 sentences).",
      "tags": ["debate", "argumentation", "summary"],
      "folderId": null,
      "createdAt": "2023-10-03T16:30:00.000Z",
      "lastUsedAt": "2023-10-17T16:35:00.000Z",
      "timesUsed": 16
    },
    {
      "id": "c6929732-5b29-4e29-13d7-a47981920336",
      "originalPromptId": "opid-020",
      "version": 1,
      "title": "Explain Like I'm Five (ELI5)",
      "description": "Explains a complex topic in very simple terms.",
      "promptText": "Explain '{{complex_topic}}' to me like I'm five years old.",
      "tags": ["explanation", "eli5", "simplification"],
      "folderId": null,
      "createdAt": "2023-09-01T11:00:00.000Z",
      "lastUsedAt": "2023-11-04T11:05:00.000Z",
      "timesUsed": 35
    }
  ],
  "folders": [
    {
      "id": "folder-mktg-01",
      "name": "Marketing Content",
      "parentId": null,
      "isDeletable": true,
      "isRenamable": true
    },
    {
      "id": "folder-code-02",
      "name": "Coding Assistants",
      "parentId": null,
      "isDeletable": true,
      "isRenamable": true
    },
    {
      "id": "folder-creative-03",
      "name": "Creative Writing",
      "parentId": null,
      "isDeletable": true,
      "isRenamable": true
    },
    {
      "id": "folder-social-sub",
      "name": "Social Media",
      "parentId": "folder-mktg-01",
      "isDeletable": true,
      "isRenamable": true
    }
  ]
}
</file>

<file path="index.html">
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-P8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>AI Prompt Manager</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        darkMode: 'class', // Enable dark mode using the class strategy
        theme: {
          extend: {
            colors: {
              primary: {
                DEFAULT: '#1E40AF', // A deep blue
                light: '#3B82F6',
                dark: '#1E3A8A',
              },
              secondary: '#0D9488', // A teal
              neutral: {
                '50': '#F8FAFC',
                '100': '#F1F5F9',
                '200': '#E2E8F0',
                '300': '#CBD5E1',
                '400': '#94A3B8',
                '500': '#64748B',
                '600': '#475569',
                '700': '#334155',
                '800': '#1E293B',
                '900': '#0F172A',
              },
            },
            fontFamily: {
              sans: ['Inter', 'sans-serif'],
            },
          }
        }
      }
    </script>
    <link rel="stylesheet" href="https://rsms.me/inter/inter.css">
  <script type="importmap">
{
  "imports": {
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "react": "https://esm.sh/react@^19.1.0",
    "react/": "https://esm.sh/react@^19.1.0/",
    "idb": "https://esm.sh/idb@8?bundle"
  }
}
</script>
<link rel="stylesheet" href="/index.css">
</head>
  <body class="bg-neutral-100 dark:bg-neutral-900 transition-colors duration-300">
    <div id="root"></div>
    <script type="module" src="/index.tsx"></script>
  </body>
</html>
</file>

<file path="index.tsx">
import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App.tsx'; // Changed this line

const rootElement = document.getElementById('root');
if (!rootElement) {
  throw new Error("Could not find root element to mount to");
}

const root = ReactDOM.createRoot(rootElement);
root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);
</file>

<file path="metadata.json">
{
  "name": "AI Prompt Manager",
  "description": "A slick, modern UI to manage your AI prompts. Add, edit, delete, and organize prompts with quick copy-to-clipboard functionality, usage tracking, and sorting options.",
  "requestFramePermissions": [],
  "prompt": ""
}
</file>

<file path="package.json">
{
  "name": "ai-prompt-manager",
  "private": true,
  "version": "0.0.0",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "preview": "vite preview"
  },
  "dependencies": {
    "react-dom": "^19.1.0",
    "react": "^19.1.0",
    "idb": "^7.1.1"
  },
  "devDependencies": {
    "@types/node": "^22.14.0",
    "typescript": "~5.7.2",
    "vite": "^6.2.0"
  }
}
</file>

<file path="README.md">
# Run and deploy your AI Studio app

This contains everything you need to run your app locally.

## Run Locally

**Prerequisites:**  Node.js


1. Install dependencies:
   `npm install`
2. Set the `GEMINI_API_KEY` in [.env.local](.env.local) to your Gemini API key
3. Run the app:
   `npm run dev`
</file>

<file path="tsconfig.json">
{
  "compilerOptions": {
    "target": "ES2020",
    "experimentalDecorators": true,
    "useDefineForClassFields": false,
    "module": "ESNext",
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "isolatedModules": true,
    "moduleDetection": "force",
    "noEmit": true,
    "allowJs": true,
    "jsx": "react-jsx",

    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedSideEffectImports": true,

    "paths": {
      "@/*" :  ["./*"]
    }
  }
}
</file>

<file path="types.ts">
import type { DBSchema } from 'idb';

export interface Prompt {
  id: string; // Unique ID for this specific version
  originalPromptId: string; // ID of the first version of this prompt. For the first version, id === originalPromptId.
  version: number; // Version number, starting from 1
  title: string;
  description: string;
  promptText: string;
  tags: string[];
  folderId: string | null; // ID of the folder it belongs to
  createdAt: string; // ISO date string (when this version was created)
  lastUsedAt: string | null; // ISO date string
  timesUsed: number;
}

export interface Folder {
  id: string;
  name: string;
  parentId: string | null; // ID of the parent folder, or null if it's a root folder
  isDeletable: boolean;
  isRenamable: boolean;
  children?: HierarchicalFolder[]; // transient property for UI, not stored directly in DB as such
  level?: number; // for indentation in dropdowns, transient
}

export interface HierarchicalFolder extends Folder {
  children: HierarchicalFolder[];
  level: number;
}

export interface HierarchicalTag {
  name: string;
  fullName: string;
  children: HierarchicalTag[];
}

export enum SortOption {
  NAME_ASC = 'name_asc',
  NAME_DESC = 'name_desc',
  MOST_USED = 'most_used',
  RECENTLY_USED = 'recently_used',
  DATE_CREATED_ASC = 'date_created_asc', // Based on the version's createdAt
  DATE_CREATED_DESC = 'date_created_desc', // Based on the version's createdAt
}

// Structure for JSON import/export
export interface ExportData {
  prompts: Prompt[];
  folders: Folder[];
  // Could add app settings/metadata here in the future
}

// Schema for IndexedDB
export interface AIPMPRDB extends DBSchema {
  prompts: {
    key: string; // id of the prompt version
    value: Prompt;
    indexes: {
      originalPromptId: string;
      folderId: string;
      version: number;
      createdAt: string;
      lastUsedAt: string;
      title: string;
    };
  };
  folders: {
    key: string; // id of the folder
    value: Folder;
    indexes: {
      parentId: string;
      name: string;
    };
  };
}
</file>

<file path="vite.config.ts">
import path from 'path';
import { defineConfig, loadEnv } from 'vite';

export default defineConfig(({ mode }) => {
    const env = loadEnv(mode, '.', '');
    return {
      define: {
        'process.env.API_KEY': JSON.stringify(env.GEMINI_API_KEY),
        'process.env.GEMINI_API_KEY': JSON.stringify(env.GEMINI_API_KEY)
      },
      resolve: {
        alias: {
          '@': path.resolve(__dirname, '.'),
        }
      }
    };
});
</file>

</files>
