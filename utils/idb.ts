

import { openDB, IDBPDatabase, IDBPTransaction } from 'idb';
import { Prompt, AIPMPRDB, ExportData } from '../types';

const DB_NAME = 'AIPromptManagerDB';
const DB_VERSION = 2; // Incremented version to trigger upgrade
const PROMPTS_STORE_NAME = 'prompts';

let dbPromise: Promise<IDBPDatabase<AIPMPRDB>> | null = null;

const getDB = (): Promise<IDBPDatabase<AIPMPRDB>> => {
  if (!dbPromise) {
    dbPromise = openDB<AIPMPRDB>(DB_NAME, DB_VERSION, {
      upgrade(db: IDBPDatabase<AIPMPRDB>, oldVersion: number, newVersion: number | null, tx: IDBPTransaction<AIPMPRDB, (typeof PROMPTS_STORE_NAME)[], "versionchange">, event: IDBVersionChangeEvent) {
        console.log(`Upgrading DB from version ${oldVersion} to ${newVersion}`);
        if (!db.objectStoreNames.contains(PROMPTS_STORE_NAME)) {
          const promptsStore = db.createObjectStore(PROMPTS_STORE_NAME, { keyPath: 'id' });
          promptsStore.createIndex('originalPromptId', 'originalPromptId');
          promptsStore.createIndex('version', 'version');
          promptsStore.createIndex('createdAt', 'createdAt');
          promptsStore.createIndex('lastUsedAt', 'lastUsedAt');
          promptsStore.createIndex('title', 'title');
        }
        if (db.objectStoreNames.contains('folders')) {
          db.deleteObjectStore('folders');
        }
      },
      blocked(currentVersion: number, blockedVersion: number | null, event: IDBVersionChangeEvent) {
        console.error(`IndexedDB open blocked. Current version: ${currentVersion}, Attempted version: ${blockedVersion}. Please close other tabs running this application and refresh.`);
        alert("The application database is blocked by an older version in another tab. Please close other tabs and refresh the page.");
      },
      blocking(this: IDBPDatabase<AIPMPRDB>, currentVersion: number, blockedVersion: number | null, event: IDBVersionChangeEvent) {
        console.warn(`IndexedDB connection (version ${currentVersion}) is blocking an attempt to open version ${blockedVersion}. Closing this blocking connection.`);
        this.close(); 
        alert("The application database needs to upgrade. This tab was blocking it and has closed its database connection. Please refresh the page for the changes to take effect.");
      },
      terminated() {
        console.warn('IndexedDB connection was terminated by the browser.');
        dbPromise = null; 
      }
    }).catch(error => {
      console.error("Failed to open IndexedDB:", error);
      dbPromise = null; 
      throw error; 
    });
  }
  return dbPromise;
};

// Prompt Functions
export const getAllPromptsDB = async (): Promise<Prompt[]> => {
  const db = await getDB();
  return db.getAll(PROMPTS_STORE_NAME);
};

export const getPromptByIdDB = async (id: string): Promise<Prompt | undefined> => {
  const db = await getDB();
  return db.get(PROMPTS_STORE_NAME, id);
};

export const getPromptsByOriginalIdDB = async (originalPromptId: string): Promise<Prompt[]> => {
  const db = await getDB();
  return db.getAllFromIndex(PROMPTS_STORE_NAME, 'originalPromptId', originalPromptId);
};

export const addPromptDB = async (prompt: Prompt): Promise<string> => {
  const db = await getDB();
  return db.add(PROMPTS_STORE_NAME, prompt);
};

export const updatePromptDB = async (prompt: Prompt): Promise<string> => {
  const db = await getDB();
  return db.put(PROMPTS_STORE_NAME, prompt);
};

export const deletePromptsByOriginalIdDB = async (originalPromptId: string): Promise<void> => {
  const db = await getDB();
  const tx = db.transaction(PROMPTS_STORE_NAME, 'readwrite');
  const store = tx.objectStore(PROMPTS_STORE_NAME);
  const index = store.index('originalPromptId');
  let cursor = await index.openCursor(originalPromptId);
  while (cursor) {
    await store.delete(cursor.primaryKey); 
    cursor = await cursor.continue();
  }
  await tx.done;
};

export const clearAllPromptsDB = async (): Promise<void> => {
  const db = await getDB();
  await db.clear(PROMPTS_STORE_NAME);
};

export const importDataDB = async (data: ExportData): Promise<void> => {
  const db = await getDB();
  const tx = db.transaction(PROMPTS_STORE_NAME, 'readwrite');
  
  const promptsStore = tx.objectStore(PROMPTS_STORE_NAME);

  await promptsStore.clear();
  
  for (const prompt of data.prompts) {
    await promptsStore.add(prompt);
  }

  return tx.done;
};
