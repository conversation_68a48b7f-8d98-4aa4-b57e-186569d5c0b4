
import { Prompt } from './types';

export interface HierarchicalTag {
  name: string;
  fullName: string;
  children: HierarchicalTag[];
}

export const buildTagHierarchy = (prompts: Prompt[]): HierarchicalTag[] => {
  const allTags = new Set<string>();
  prompts.forEach(p => p.tags.forEach(t => allTags.add(t)));

  const root: HierarchicalTag = { name: 'root', fullName: 'root', children: [] };

  allTags.forEach(tag => {
    const parts = tag.split('/');
    let currentNode = root;
    let currentFullName = '';

    parts.forEach((part, index) => {
      currentFullName = currentFullName ? `${currentFullName}/${part}` : part;
      let childNode = currentNode.children.find(c => c.name === part);
      if (!childNode) {
        childNode = { name: part, fullName: currentFullName, children: [] };
        currentNode.children.push(childNode);
      }
      currentNode = childNode;
    });
  });

  return root.children.sort((a, b) => a.name.localeCompare(b.name));
};

export const getAllTagIdsInBranch = (tag: HierarchicalTag): string[] => {
  const ids: string[] = [tag.fullName];
  tag.children.forEach(child => {
    ids.push(...getAllTagIdsInBranch(child));
  });
  return ids;
};
