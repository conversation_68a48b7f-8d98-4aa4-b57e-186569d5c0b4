
import React, { useState, useMemo, useCallback, useEffect, useRef } from 'react';
import { Prompt, SortOption, ExportData, HierarchicalTag } from './types';
import { SORT_OPTIONS_MAP, PROMPTS_PER_PAGE as IMPORTED_PROMPTS_PER_PAGE } from './constants';
import { generateUUID } from './utils/uuid';
import Header from './components/Header';
import Modal from './components/Modal';
import PromptForm from './components/PromptForm';
import HelpModal from './components/HelpModal';
import VersionHistoryModal from './components/VersionHistoryModal';
import {
  getAllPromptsDB,
  addPromptDB,
  importDataDB,
} from './utils/idb';
import useTheme from './hooks/useTheme';

const App = () => {
  const [_theme, _toggleTheme] = useTheme();
  const [allPrompts, setAllPrompts] = useState<Prompt[]>([]);
  const [isPromptModalOpen, setIsPromptModalOpen] = useState(false);
  const [promptToEdit, setPromptToEdit] = useState<Prompt | null>(null);
  const [isHelpModalOpen, setIsHelpModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isProcessingData, setIsProcessingData] = useState(false);
  const importFileRef = useRef<HTMLInputElement>(null);

  // Dummy state for modals not yet fully wired
  const [isHistoryModalOpen, setIsHistoryModalOpen] = useState(false);

  const loadData = useCallback(async (context: string = "initial") => {
    console.log(`[loadData called from ${context}] Starting data load...`);
    setIsLoading(true);
    try {
      console.log(`[loadData from ${context}] Attempting to get all prompts from DB...`);
      let dbPrompts = await getAllPromptsDB();
      console.log(`[loadData from ${context}] Successfully fetched ${dbPrompts.length} prompts.`);
      setAllPrompts(dbPrompts);
      console.log(`[loadData from ${context}] Prompts set in state.`);
    } catch (error) {
      console.error(`[loadData from ${context}] Error loading data from IndexedDB:`, error);
      alert("Error loading data. Please check the console for details.");
    } finally {
      setIsLoading(false);
      console.log(`[loadData from ${context}] Loading finished. isLoading set to false.`);
    }
  }, []);

  useEffect(() => {
    loadData("initial useEffect");
  }, [loadData]);

  const handleAddPromptClick = () => {
    setPromptToEdit(null);
    setIsPromptModalOpen(true);
  };

  const handleSavePrompt = async (
    promptData: Omit<Prompt, 'id' | 'originalPromptId' | 'version' | 'createdAt' | 'lastUsedAt' | 'timesUsed'>,
    existingPromptVersion?: Prompt | null
  ) => {
    let newPromptVersion: Prompt;
    if (existingPromptVersion) {
      newPromptVersion = {
        ...promptData,
        id: generateUUID(),
        originalPromptId: existingPromptVersion.originalPromptId,
        version: existingPromptVersion.version + 1,
        createdAt: new Date().toISOString(),
        lastUsedAt: null, 
        timesUsed: 0,    
      };
    } else {
      const newId = generateUUID();
      newPromptVersion = {
        ...promptData,
        id: newId,
        originalPromptId: newId,
        version: 1,
        createdAt: new Date().toISOString(),
        lastUsedAt: null,
        timesUsed: 0,
      };
    }
    await addPromptDB(newPromptVersion);
    setAllPrompts(prev => [...prev, newPromptVersion]);
    setIsPromptModalOpen(false);
    setPromptToEdit(null);
  };

  const handleExportData = async () => {
    setIsProcessingData(true);
    try {
      const promptsToExport = await getAllPromptsDB();
      const exportData: ExportData = { prompts: promptsToExport };
      const jsonString = JSON.stringify(exportData, null, 2);
      const blob = new Blob([jsonString], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `ai-prompt-manager-backup-${new Date().toISOString().slice(0, 10)}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      alert('Data exported successfully!');
    } catch (error) {
      console.error('Error exporting data:', error);
      alert('Failed to export data. Check console for details.');
    } finally {
      setIsProcessingData(false);
    }
  };

  const handleImportDataTrigger = () => {
    if (importFileRef.current) {
      importFileRef.current.click();
    }
  };

  const handleImportFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;
    if (!window.confirm("WARNING: Importing data will replace ALL current prompts. This action CANNOT BE UNDONE. Are you sure?")) {
      if (importFileRef.current) importFileRef.current.value = '';
      return;
    }
    setIsProcessingData(true);
    try {
      const jsonString = await file.text();
      const importedData = JSON.parse(jsonString) as ExportData;
      if (!importedData || !Array.isArray(importedData.prompts)) throw new Error('Invalid file format.');
      await importDataDB(importedData);
      alert('Data imported successfully! Reloading...');
      await loadData("after import");
    } catch (error: any) {
      alert(`Failed to import data: ${error.message}`);
      console.error('Import error:', error);
    } finally {
      setIsProcessingData(false);
      if (importFileRef.current) importFileRef.current.value = '';
    }
  };

  if (isLoading || isProcessingData) {
    return (
      <div className="flex items-center justify-center h-screen bg-neutral-100 dark:bg-neutral-900">
        <div className="text-2xl font-semibold text-neutral-700 dark:text-neutral-200">
          {isProcessingData ? 'Processing Data...' : 'Loading Prompts...'}
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen flex-col bg-neutral-100 dark:bg-neutral-900">
      <Header 
        onAddPrompt={handleAddPromptClick} 
        onToggleHelp={() => setIsHelpModalOpen(true)}
        onExportData={handleExportData}
        onImportDataTrigger={handleImportDataTrigger}
      />
      <input type="file" accept=".json" ref={importFileRef} onChange={handleImportFileSelect} style={{ display: 'none' }} />
      
      <div className="flex flex-1 overflow-hidden">
        <main className="flex-1 p-6 overflow-y-auto">
          <h1 className="text-2xl font-bold text-neutral-900 dark:text-neutral-100">Core Logic Restored</h1>
          <p className="text-neutral-800 dark:text-neutral-200">The main application logic and Header are now active. Next, we will add the Sidebar and the main content.</p>
        </main>
      </div>

      {isPromptModalOpen && (
        <Modal isOpen={isPromptModalOpen} onClose={() => setIsPromptModalOpen(false)} title={promptToEdit ? 'Edit Prompt (New Version)' : 'Add New Prompt'}>
          <PromptForm promptToEdit={promptToEdit} onSave={handleSavePrompt} onClose={() => { setIsPromptModalOpen(false); setPromptToEdit(null); }} />
        </Modal>
      )}

      {isHelpModalOpen && (<HelpModal isOpen={isHelpModalOpen} onClose={() => setIsHelpModalOpen(false)} />)}
      
      {/* Dummy placeholder for history modal */}
      {isHistoryModalOpen && (<VersionHistoryModal isOpen={isHistoryModalOpen} onClose={() => setIsHistoryModalOpen(false)} promptVersions={[]} originalPromptTitle="" />)}

    </div>
  );
};

export default App;
