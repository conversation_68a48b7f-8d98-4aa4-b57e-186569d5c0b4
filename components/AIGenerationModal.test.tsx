import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import AIGenerationModal from './AIGenerationModal';
import IconButton from './IconButton';
import Modal from './Modal';
import ClipboardIcon from './ClipboardIcon';

vi.mock('./IconButton', () => ({
  default: (props) => <div onClick={props.onClick}>Mock IconButton</div>,
}));
vi.mock('./Modal', () => ({
  default: ({ children }) => <div>{children}</div>,
}));

describe('AIGenerationModal Component Tests', () => {
  let mockProps, mockOnClose, mockOnGenerate;

  beforeEach(() => {
    mockProps = {
      promptTitle: "Test Prompt",
      inputText: "Prompt input text for AI generation",
      generatedText: null,
      isGenerating: false,
      isOpen: true,
      onGenerate: vi.fn(),
      onClose: vi.fn(),
    };

    mockOnClose = mockProps.onClose;
    mockOnGenerate = mockProps.onGenerate;
  });

  it('renders input read-only field with inputText value', () => {
    render(<AIGenerationModal {...mockProps} />);
    expect(screen.getByLabelText(/Input prompt text/i).value).toEqual(mockProps.inputText);
    expect(screen.getByLabelText(/Input prompt text/i)).toBeEnabled();
    expect(screen.getByLabelText(/Input prompt text/i)).toHaveAttribute('readOnly');
  });

  it('triggers onGenerate when button clicked', async () => {
    render(<AIGenerationModal {...mockProps} />);

    fireEvent.click(screen.getByRole('button', { name: "Generate Text"}));
    expect(mockOnGenerate).toHaveBeenCalledWith(mockProps.inputText);
  });

  it('shows "Generate Text" initially and "Regenerate" when there is generatedText', () => {
    render(<AIGenerationModal {...mockProps} />);
    expect(screen.getByText("Generate Text")).toBeVisible();
    expect(screen.queryByText("Regenerate")).toBeNull();

    mockProps.generatedText = "Generated text example";
    render(<AIGenerationModal {...mockProps} />);
    expect(screen.getByText("Regenerate")).toBeVisible();
    expect(screen.queryByText("Generate Text")).toBeNull();
  });

  it('properly handles error state and hides generated content', () => {
    mockProps.generatedText = "";
    mockProps.errorMessage = "Request failed: server error.";

    render(<AIGenerationModal {...mockProps} />);

    const errorDisplay = screen.getAllByRole('heading').find(e => e.textContent.startsWith("Whoops!"));
    expect(errorDisplay).toBeTruthy();
    expect(screen.queryByLabelText(/AI generated text output/i)).toBeNull();
    expect(screen.queryByText("Copy Output")).toBeNull();
  });

  it('provides copy to clipboard functionality on AI output', () => {
    mockProps.generatedText = "This is the AI-generated content.";
    render(<AIGenerationModal {...mockProps} />);

    const textarea = screen.getByText("Copy Output").closest('div').nextElementSibling;
    expect(textarea).toHaveDisplayValue("This is the AI-generated content.");
    expect(textarea).toBeReadOnly();

    fireEvent.click(screen.getByText("Copy Output"));
    expect(navigator.clipboard.writeText).toHaveBeenCalled();
  });

  it('displays loading state when isGenerating=true', () => {
    mockProps.isGenerating = true;
    render(<AIGenerationModal {...mockProps} />);

    expect(screen.queryByText(/Generate Text/i)).toBeDisabled();
    expect(screen.queryByText(/Regenerate/i)).toBeDisabled();

    const spinner = screen.getByTestId('generate-spinner');
    expect(spinner).toBeVisible();
    expect(spinner.tagName).toBe("SVG");

    expect(screen.queryByLabelText(/AI generated text output/i)).toBeNull();
    expect(screen.queryByText("Copy Output")).toBeNull();
  });
});
