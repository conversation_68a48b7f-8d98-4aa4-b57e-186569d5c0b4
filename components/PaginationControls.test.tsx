import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import PaginationControls from './PaginationControls';
import IconButton from './IconButton';
import ChevronLeftIcon from './icons/ChevronLeftIcon';
import ChevronRightIcon from './icons/ChevronRightIcon';

vi.mock('./IconButton', () => ({
  default: ({ onClick, children }) => <button onClick={onClick} className="mocked-icon-button">{children}</button>,
}));
vi.mock('./icons/ChevronLeftIcon', () => ({
  default: () => <svg><path className="left-svg-path"></path></svg>,
}));
vi.mock('./icons/ChevronRightIcon', () => ({
  default: () => <svg><path className="right-svg-path"></path></svg>,
}));

describe('PaginationControls Component Tests', () => {
  let mockHandler;

  beforeEach(() => {
    mockHandler = vi.fn();
  });

  it('renders no pagination controls when total pages <= 1', () => {
    render(<PaginationControls currentPage={1} totalPages={1} onPageChange={mockHandler} />);
    expect(screen.queryByRole('navigation')).toBeNull();
  });

  it('displays correct page numbers in range with current page highlighted', () => {
    render(<PaginationControls currentPage={4} totalPages={9} onPageChange={mockHandler} />);

    // Check all displayed number elements
    expect(screen.getAllByRole('button').map(btn => btn.textContent)).toEqual(["2", "3", "4", "5", "6"]);
    // Verify current page button class
    expect(screen.getByText('4')).toHaveClass('page-item current-page bg-primary');
  });

  it('navigates to previous page when "Previous" button clicked', () => {
    render(<PaginationControls currentPage={4} totalPages={9} onPageChange={mockHandler} />);

    fireEvent.click(screen.getByText('Previous'));
    expect(mockHandler).toHaveBeenCalledWith(3);
  });

  it('navigates to next page when "Next" button clicked', () => {
    render(<PaginationControls currentPage={4} totalPages={9} onPageChange={mockHandler} />);

    fireEvent.click(screen.getByText('Next'));
    expect(mockHandler).toHaveBeenCalledWith(5);
  });

  it('disables Previous button on first page, Next button on last page', () => {
    // First page
    render(<PaginationControls currentPage={1} totalPages={5} onPageChange={mockHandler} />);
    const prevButtonFirstPage = screen.getAllByRole('button')[0];
    expect(prevButtonFirstPage.closest('button')).toHaveAttribute('disabled');

    // Last page
    render(<PaginationControls currentPage={5} totalPages={5} onPageChange={mockHandler} />);
    const nextButtonLastPage = screen.getAllByRole('button').at(-1);
    expect(nextButtonLastPage.closest('button')).toHaveAttribute('disabled');

    // Test navigation between pages
    fireEvent.click(screen.getByText('2'));
    expect(mockHandler).toHaveBeenCalledWith(2);
    mockHandler.mockClear();

    // Middle page with full range
    render(<PaginationControls currentPage={3} totalPages={7} onPageChange={mockHandler} />);
    const buttons = screen.getAllByRole('button').map(btn => btn.textContent);
    expect(buttons).toEqual(["Previous", "1", "2", "3", "4", "5", "Next"]);
  });
});
