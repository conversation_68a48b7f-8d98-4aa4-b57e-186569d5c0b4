import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import Header from './Header';

// Mock all dependencies to isolate Header functionality
vi.mock('./PlusIcon', () => ({
  default: () => <svg className="plus-icon-mock" />,
}));
vi.mock('./ThemeToggleButton', () => ({
  default: () => <div className="theme-toggle-mock">Toggle Button</div>,
}));
vi.mock('./QuestionMarkCircleIcon', () => ({
  default: () => <svg className="question-icon-mock" />,
}));
vi.mock('./DownloadIcon', () => ({
  default: () => <svg className="download-icon-mock" />,
}));
vi.mock('./UploadIcon', () => ({
  default: () => <svg className="upload-icon-mock" />,
}));
vi.mock('./IconButton', () => ({
  default: ({ onClick, children }) => <button onClick={onClick} className="icon-button-mock">{children}</button>,
}));

describe('Header Component Tests', () => {
  let mockProps;

  beforeEach(() => {
    mockProps = {
      onAddPrompt: vi.fn(),
      onToggleHelp: vi.fn(),
      onExportData: vi.fn(),
      onImportDataTrigger: vi.fn(),
    };
  });

  afterEach(() => {
    vi.resetAllMocks(); // Reset all mocks after each test
  });

  it('renders title structure and branding correctly', () => {
    render(<Header {...mockProps} />);

    const title = screen.getByText('AI Prompt Manager');
    const emoji = screen.getByTitle('brain emoji');

    expect(title).toBeVisible();
    expect(emoji.innerHTML).toContain('🧠');
    expect(emoji).toHaveAttribute('role', 'img');

    const appBrand = screen.getByTitle('App Brand');
    expect(appBrand).toBeTruthy();
  });

  it('shows both short and full Add buttons based on screen size', () => {
    // Simulate smaller viewport (<= 640px)
    Object.defineProperty(window, 'innerWidth', { value: 500 });
    render(<Header {...mockProps} />);

    expect(screen.queryByText('Add Prompt')).toBeNull();
    expect(screen.getByText('Add')).toBeVisible();

    // Reset to desktop viewport
    Object.defineProperty(window, 'innerWidth', { value: 1000 });
    render(<Header {...mockProps} />);

    expect(screen.getByText('Add Prompt')).toBeVisible();
    expect(screen.queryByText('Add')).toBeNull();
  });

  it('triggers all action handlers when buttons clicked', async () => {
    render(<Header {...mockProps} />);

    fireEvent.click(screen.getByText('Add Prompt')); // Using desktop mode
    expect(mockProps.onAddPrompt).toHaveBeenCalled();

    fireEvent.click(screen.getByTitle('Help'));
    expect(mockProps.onToggleHelp).toHaveBeenCalled();

    fireEvent.click(screen.getByLabelText('Export Data'));
    expect(mockProps.onExportData).toHaveBeenCalled();

    fireEvent.click(screen.getByLabelText('Import Data'));
    expect(mockProps.onImportDataTrigger).toHaveBeenCalled();
  });

  it('ensures title structure changes between viewport sizes', () => {
    render(<Header {...mockProps} />);
    Object.defineProperty(window, 'innerWidth', { value: 450 });
    window.dispatchEvent(new Event('resize'));

    const appTitle = screen.getByText('Prompts');
    expect(appTitle).toBeVisible();
    expect(screen.queryByText('AI Prompt Manager')).toBeNull();
  });

  it('properly applies button state from isDesktop prop override', () => {
    render(<Header {...mockProps} />);
    expect(screen.queryByRole('button', { name: 'Desktop Add' })).toBeNull();

    expect(screen.getByLabelText('Add Prompt').closest('button')).toBeTruthy();
    expect(screen.getByLabelText('Help').closest('span')).toBeTruthy();

    // Verify default button aria-labels are correct
    expect(screen.getByRole('button', { name: 'Add Prompt' }).closest('button').title).toBe('Add New Prompt');
  });
});
