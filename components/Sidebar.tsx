

import React, { useState, useMemo } from 'react';
import { HierarchicalTag } from '../types';
import TagIcon from './icons/TagIcon';
import ChevronRightIcon from './icons/ChevronRightIcon';
import ChevronDownIcon from './icons/ChevronDownIcon';
import IconButton from './IconButton';

interface TagItemProps {
    tag: HierarchicalTag;
    selectedTag: string | null;
    onSelectTag: (tag: string | null) => void;
    isExpanded: boolean;
    onToggleExpand: (fullName: string) => void;
    level: number;
}

const TagItem: React.FC<TagItemProps> = ({ tag, selectedTag, onSelectTag, isExpanded, onToggleExpand, level }) => {
    const isSelected = selectedTag === tag.fullName;
    return (
        <div style={{ paddingLeft: `${level * 1.25}rem` }}>
            <div
                className={`group w-full flex items-center justify-between px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                    isSelected
                        ? 'bg-primary-light text-white'
                        : 'text-neutral-700 dark:text-neutral-200 hover:bg-neutral-200 dark:hover:bg-neutral-700 hover:text-neutral-900 dark:hover:text-neutral-50'
                }`}
            >
                <button onClick={() => onSelectTag(tag.fullName)} className="flex items-center flex-grow truncate mr-2">
                    {tag.children.length > 0 && (
                        <IconButton
                            onClick={(e) => { e.stopPropagation(); onToggleExpand(tag.fullName); }}
                            className={`mr-1 p-0.5 ${isSelected ? 'text-white hover:bg-primary dark:hover:bg-primary-dark' : 'text-neutral-500 dark:text-neutral-400 hover:bg-neutral-300 dark:hover:bg-neutral-600'}`}
                            label={isExpanded ? "Collapse" : "Expand"}
                        >
                            {isExpanded ? <ChevronDownIcon className="w-4 h-4" /> : <ChevronRightIcon className="w-4 h-4" />}
                        </IconButton>
                    )}
                    <TagIcon className={`w-5 h-5 mr-2 ${tag.children.length === 0 ? 'ml-[1.125rem]' : ''} ${isSelected ? 'text-white' : 'text-neutral-500 dark:text-neutral-400'}`} />
                    <span className="truncate">{tag.name}</span>
                </button>
            </div>
            {isExpanded && tag.children.length > 0 && (
                <div className="mt-1 space-y-1">
                    {/* Recursive rendering handled by parent in renderTagItems */}
                </div>
            )}
        </div>
    );
};

interface SidebarProps {
  hierarchicalTags: HierarchicalTag[];
  selectedTag: string | null;
  onSelectTag: (tag: string | null) => void;
}

const Sidebar: React.FC<SidebarProps> = ({
  hierarchicalTags,
  selectedTag,
  onSelectTag,
}) => {
  const [expandedTags, setExpandedTags] = useState<Record<string, boolean>>({});

  const handleToggleTagExpand = (fullName: string) => {
    setExpandedTags(prev => ({ ...prev, [fullName]: !prev[fullName] }));
  };

  const renderTagItems = (tagsToRender: HierarchicalTag[], level: number) => {
    return tagsToRender.map(tag => (
      <React.Fragment key={tag.fullName}>
        <TagItem
          tag={tag}
          selectedTag={selectedTag}
          onSelectTag={onSelectTag}
          isExpanded={!!expandedTags[tag.fullName]}
          onToggleExpand={handleToggleTagExpand}
          level={level}
        />
        {expandedTags[tag.fullName] && tag.children && tag.children.length > 0 && (
          <div className="pl-0 mt-1 space-y-1">
            {renderTagItems(tag.children, level + 1)}
          </div>
        )}
      </React.Fragment>
    ));
  };

  return (
    <aside className="w-72 bg-neutral-50 dark:bg-neutral-800 p-4 border-r border-neutral-200 dark:border-neutral-700 space-y-6 h-full overflow-y-auto flex flex-col transition-colors duration-300">
      <div className="flex-grow">
        <h2 className="text-xs font-semibold text-neutral-500 dark:text-neutral-400 uppercase tracking-wider mb-3 px-3">
          Tags
        </h2>
        <nav className="space-y-1">
          <button
            onClick={() => onSelectTag(null)}
            className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors
                        ${selectedTag === null 
                          ? 'bg-primary-light text-white' 
                          : 'text-neutral-700 dark:text-neutral-200 hover:bg-neutral-200 dark:hover:bg-neutral-700 hover:text-neutral-900 dark:hover:text-neutral-50'}`}
          >
            <TagIcon className={`w-5 h-5 mr-3 ${selectedTag === null ? 'text-white' : 'text-neutral-500 dark:text-neutral-400'}`} />
            All Tags
          </button>
          {renderTagItems(hierarchicalTags, 0)}
        </nav>
      </div>
    </aside>
  );
};

export default Sidebar;
